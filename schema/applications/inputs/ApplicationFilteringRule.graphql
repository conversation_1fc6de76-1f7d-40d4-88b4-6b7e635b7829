input ApplicationFilteringRule {
    """
    Filter by bank
    """
    bank: String

    """
    Filter by finance product
    """
    financeProduct: String

    inventoryStockId: ObjectID

    """
    Filter by vehicle
    """
    vehicle: String

    """
    Filter by application module ID
    """
    moduleIds: [ObjectID!]

    """
    Filter by event ID
    """
    eventId: ObjectID

    """
    Filter by application stage
    """
    stage: ApplicationStage

    """
    Application Status
    """
    status: [ApplicationStatus!]

    """
    Dealer Name
    """
    dealerName: String

    """
    Module Name
    """
    moduleName: String

    """
    Current Month
    """
    currentMonth: Boolean

    """
    Last Month
    """
    lastMonth: Boolean

    """
    Search Keyword
    """
    keyword: String

    """
    Filter by dealer ids
    """
    dealerIds: [ObjectID!]

    """
    Filter by applicant IDs (customer ID)
    """
    applicantIds: [ObjectID!]

    """
    Filter by company ID
    """
    companyIds: [ObjectID!]

    """
    Filter by configurator ID
    """
    configuratorId: ObjectID

    """
    Filter by kind
    """
    kind: ApplicationKind

    """
    Filter by kind
    """
    identifier: String

    """
    Filter by Assignee
    """
    assignee: String

    """
    Filter by TransactionId
    """
    transactionId: String

    """
    Mobility Booking Location
    """
    locations: [String!]

    """
    Filter by vehicle names
    """
    vehicleNames: [String!]

    """
    Filter by customer fullName
    """
    customer: String

    """
    Filter by customer Business Partner ID
    """
    businessPartnerId: String

    """
    Filter by Booking Start Date Range
    """
    startDateRange: PeriodPayload

    """
    Filter by Booking End Date Range
    """
    endDateRange: PeriodPayload

    """
    Filter by Booking Submitted Date Range
    """
    createdDateRange: PeriodPayload

    """
    Filter by Appointment Date Range
    """
    appointmentDateRange: PeriodPayload

    """
    Filter by stock vin
    """
    vin: String

    """
    Filter by insurer
    """
    insurer: String

    """
    Filter by Finder Vehicle VIN
    """
    finderVehicleVin: String

    """
    Filter by event display name
    """
    eventDisplayName: String

    """
    Filter by event campaign ID
    """
    eventCampaignId: String

    """
    Lead Stage
    """
    leadStage: LeadStageOption

    """
    Appointment Kind
    """
    appointmentModuleFilter: ModuleType
}
