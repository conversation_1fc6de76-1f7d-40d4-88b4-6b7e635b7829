input LeadFilteringRule {
    """
    Filter by vehicle
    """
    vehicle: String

    """
    Filter by application module ID
    """
    moduleIds: [ObjectID!]

    """
    Filter by event ID
    """
    eventId: ObjectID

    """
    Lead Status
    """
    status: [LeadStatus!]

    """
    Dealer Name
    """
    dealerName: String

    """
    Module Name
    """
    moduleName: String

    """
    Current Month
    """
    currentMonth: Boolean

    """
    Last Month
    """
    lastMonth: Boolean

    """
    Search Keyword
    """
    keyword: String

    """
    Filter by dealer ids
    """
    dealerIds: [ObjectID!]

    """
    Filter by customer IDs (customer ID)
    """
    customerIds: [ObjectID!]

    """
    Filter by company ID
    """
    companyIds: [ObjectID!]

    """
    Filter by kind
    """
    identifier: String

    """
    Filter by Assignee
    """
    assignee: String

    """
    Filter by vehicle names
    """
    vehicleNames: [String!]

    """
    Filter by Finder Vehicle VIN
    """
    finderVehicleVin: String

    """
    Filter by Configurator ID
    """
    configuratorId: ObjectID

    """
    Filter by customer fullName
    """
    customer: String

    """
    Filter by customer Business Partner ID
    """
    businessPartnerId: String

    """
    Filter by Lead Submitted Date Range
    """
    createdDateRange: PeriodPayload

    """
    Filter by stock vin
    """
    vin: String

    """
    Lead Stage
    """
    leadStage: LeadStageOption

    """
    Filter by event display name
    """
    eventDisplayName: String

    """
    Filter by event campaign ID
    """
    eventCampaignId: String

    """
    Include merged status
    """
    includeMergedStatus: Boolean
}
