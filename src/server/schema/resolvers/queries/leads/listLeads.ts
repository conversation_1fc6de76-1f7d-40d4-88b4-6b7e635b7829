import dayjs from 'dayjs';
import { Document, Filter, Sort } from 'mongodb';
import { type Lead, LeadStageOption } from '../../../../database/documents';
import { LeadStatus } from '../../../../database/documents/Lead';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { Loaders } from '../../../../loaders';
import { LeadPolicyAction } from '../../../../permissions/types/leads';
import { getLeadModuleFilter } from '../../../../utils/lead/leadModuleFilter';
import { type LeadModuleExtended } from '../../../../utils/lead/types';
import { getSortingValue, paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import {
    GraphQLQueryResolvers,
    LeadSortingField,
    GraphQLLeadFilteringRule,
    GraphQLLeadSortingRule,
    Maybe,
} from '../../definitions';
import {
    lookUpCompaniesPipelines,
    lookUpModulesPipelines,
    lookUpDealerPipelines,
    lookUpVehiclePipelines,
    lookUpCustomerPipelines,
    lookUpUserPipelines,
    lookUpConfiguratorPipelines,
    lookUpEventPipelines,
} from './helper';

const getSort = (rule?: Maybe<GraphQLLeadSortingRule>): Sort => {
    const sort: Sort = { _id: 1 };

    if (!rule) {
        return sort;
    }

    switch (rule.field) {
        case LeadSortingField.LeadStatus:
            return { status: getSortingValue(rule.order), ...sort };

        case LeadSortingField.LeadGenFormCampaignId:
            return { 'campaignValues.capCampaignId': getSortingValue(rule.order), ...sort };

        case LeadSortingField.LeadGenFormName:
            return { 'event.displayName': getSortingValue(rule.order), ...sort };

        case LeadSortingField.Company:
            return { 'module.company.displayName': getSortingValue(rule.order), ...sort };

        case LeadSortingField.Identifier:
            return { identifier: getSortingValue(rule.order), ...sort };

        case LeadSortingField.Vehicle:
            return { 'vehicle.identifier': getSortingValue(rule.order), ...sort };

        case LeadSortingField.CreatedAt:
            return { '_versioning.createdAt': getSortingValue(rule.order), ...sort };

        case LeadSortingField.UpdatedAt:
            return { '_versioning.updatedAt': getSortingValue(rule.order), ...sort };

        case LeadSortingField.Assignee:
            return { 'assignee.displayName': getSortingValue(rule.order), ...sort };

        case LeadSortingField.Module:
            return { moduleDisplayName: getSortingValue(rule.order), ...sort };

        case LeadSortingField.VehicleName:
            return { 'vehicle.name.defaultValue': getSortingValue(rule.order), ...sort };

        case LeadSortingField.Customer:
            return { 'customer.fullName': getSortingValue(rule.order), ...sort };

        case LeadSortingField.BusinessPartnerId:
            return { 'capValues.businessPartnerId': getSortingValue(rule.order), ...sort };

        default:
            throw new Error('Sorting Field not supported');
    }
};

const getFilter = async (rule: Maybe<GraphQLLeadFilteringRule>, loaders: Loaders): Promise<Filter<Lead>> => {
    const baseFilter: Filter<Lead> = {
        isDraft: false,
        isDeleted: false,
    };

    const filter: Filter<Lead> = rule?.includeMergedStatus
        ? {
              $or: [
                  {
                      ...baseFilter,
                      '_versioning.isLatest': true,
                  },
                  {
                      ...baseFilter,
                      status: { $in: [LeadStatus.Merged, LeadStatus.Merging] },
                      isLead: false,
                      identifier: { $exists: true, $ne: '' },
                  },
              ],
          }
        : {
              ...baseFilter,
              '_versioning.isLatest': true,
          };

    if (!rule) {
        return filter;
    }

    if (rule.vehicle) {
        filter['vehicle.name.defaultValue'] = new RegExp(rule.vehicle, 'i');
    }

    if (rule.status) {
        filter.status = { $in: rule.status };
    }

    if (rule.leadStage && rule.leadStage !== LeadStageOption.LeadAndContact) {
        filter.isLead = rule.leadStage === LeadStageOption.Lead;
    }

    if (rule.identifier) {
        filter.identifier = new RegExp(rule.identifier, 'i');
    }

    if (rule.moduleIds?.length) {
        const modules = (await Promise.all(
            rule.moduleIds.map(moduleId => loaders.moduleById.load(moduleId))
        )) as LeadModuleExtended[];

        const moduleFilter = getLeadModuleFilter(modules, rule.companyIds);

        // Merge the returned moduleFilter into the main filter
        Object.assign(filter, moduleFilter);
    }

    if (rule.eventId) {
        filter.eventId = rule.eventId;
    }

    if (rule.eventDisplayName) {
        filter['event.displayName'] = new RegExp(rule.eventDisplayName, 'i');
    }

    if (rule.dealerIds?.length > 0) {
        filter.dealerId = { $in: rule.dealerIds };
    }

    if (rule.customerIds) {
        filter.customerId = { $in: rule.customerIds };
    }

    if (rule.dealerName) {
        filter['dealer.displayName'] = new RegExp(rule.dealerName, 'i');
    }

    if (rule.moduleName) {
        filter['module.displayName'] = new RegExp(rule.moduleName, 'i');
    }

    if (rule.companyIds?.length) {
        filter['module.companyId'] = { $in: rule.companyIds };
    }

    if (rule.assignee) {
        filter['assignee.displayName'] = new RegExp(rule.assignee, 'i');
    }

    if (rule.finderVehicleVin) {
        filter['vehicle.listing.vehicle.vin'] = new RegExp(rule.finderVehicleVin, 'i');
    }

    if (rule.customer) {
        filter['customer.fullName'] = new RegExp(rule.customer, 'i');
    }

    if (rule.eventCampaignId) {
        filter['campaignValues.capCampaignId'] = new RegExp(rule.eventCampaignId, 'i');
    }

    if (rule.createdDateRange) {
        const { start, end } = rule.createdDateRange;
        const startOfDay = dayjs(start).startOf('day').toDate();
        const endOfDay = dayjs(end).endOf('day').toDate();
        filter['_versioning.createdAt'] = { $gte: startOfDay, $lte: endOfDay };
    }

    if (rule.vin) {
        filter['inventory.stocks.vin'] = rule.vin;
    }

    if (rule.configuratorId) {
        filter['configurator.modelConfiguratorId'] = rule.configuratorId;
    }

    if (rule.businessPartnerId) {
        filter['capValues.businessPartnerId'] = new RegExp(rule.businessPartnerId, 'i');
    }

    return filter;
};

const getSearchKeyword = (keyword: string) => {
    if (!keyword) {
        return null;
    }
    const value = new RegExp(keyword, 'i');

    return {
        $or: [
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'fullName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'firstName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'lastName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'email',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'identityNumber',
            },
            {
                'customer.fields.randomizedPhone': value,
                'customer.fields.key': 'phone',
            },
            { 'assignee.displayName': value },
            { identifier: value },
            { 'vehicle.name.defaultValue': value },
        ],
    };
};

const getDateList = (rule?: Maybe<GraphQLLeadFilteringRule>) => {
    const date = new Date();
    let firstDate: Date | null;
    let lastDate: Date | null;

    if (!rule?.lastMonth && !rule?.currentMonth) {
        return null;
    }

    if (rule?.currentMonth) {
        firstDate = new Date(date.getFullYear(), date.getMonth(), 1);
        lastDate = new Date(date.getFullYear(), date.getMonth() + 1, 1);
    }

    if (rule?.lastMonth) {
        firstDate = new Date(date.getFullYear(), date.getMonth() - 1, 1);
        lastDate = new Date(date.getFullYear(), date.getMonth(), 1);
    }

    return {
        '_versioning.createdAt': { $lte: dayjs(lastDate).toDate(), $gt: dayjs(firstDate).toDate() },
    };
};

export const pipelineHandler = async (
    sort: GraphQLLeadSortingRule,
    filter: GraphQLLeadFilteringRule,
    loaders: Loaders
) => {
    const pipeline: Document[] = [];

    if (
        filter?.keyword ||
        filter?.vehicle ||
        filter?.vehicleNames ||
        filter?.finderVehicleVin ||
        sort?.field === LeadSortingField.Vehicle ||
        sort?.field === LeadSortingField.VehicleName
    ) {
        pipeline.push(...lookUpVehiclePipelines);
    }

    if (filter?.dealerName) {
        pipeline.push(...lookUpDealerPipelines);
    }

    if (
        filter?.moduleName ||
        filter?.companyIds ||
        sort?.field === LeadSortingField.Module ||
        sort?.field === LeadSortingField.Company
    ) {
        pipeline.push(...lookUpModulesPipelines);
        if (filter.companyIds || sort.field === LeadSortingField.Company) {
            pipeline.push(...lookUpCompaniesPipelines);
        }
    }

    if (filter?.keyword || filter?.assignee || sort?.field === LeadSortingField.Assignee) {
        pipeline.push(...lookUpUserPipelines);
    }

    if (filter?.keyword || filter?.customer || sort?.field === LeadSortingField.Customer) {
        pipeline.push(...lookUpCustomerPipelines);
    }

    if (filter?.configuratorId) {
        pipeline.push(...lookUpConfiguratorPipelines);
    }

    if (filter?.eventId || filter?.eventDisplayName || sort?.field === LeadSortingField.LeadGenFormName) {
        pipeline.push(...lookUpEventPipelines);
    }

    pipeline.push(
        {
            $match: {
                ...(await getFilter(filter, loaders)),
                ...getSearchKeyword(filter?.keyword),
                ...getDateList(filter),
            },
        },
        { $sort: getSort(sort) }
    );

    return pipeline;
};

const query: GraphQLQueryResolvers['listLeads'] = async (
    root,
    { pagination, sort, filter },
    { getPermissionController, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();

    const permissionFilter = await permissions.leads.getFilterQueryForActions([
        LeadPolicyAction.View,
        LeadPolicyAction.ViewContact,
    ]);

    const pipeline: Document[] = [{ $match: permissionFilter }, ...(await pipelineHandler(sort, filter, loaders))];

    return paginateAggregation(collections.leads, pipeline, pagination);
};

export default requiresLoggedUser(query);
