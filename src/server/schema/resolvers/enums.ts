export {
    AddressAutofillType,
    AddressType,
    AffinAutoFinanceCentre,
    AgeCalculationMethod,
    AmountUnit,
    ApplicationDocumentKind,
    ApplicationJourneySigningMode,
    ApplicationKind,
    ApplicationMarket,
    ApplicationQuotationDisplayOption,
    ApplicationQuotationDownPaymentTarget,
    ApplicationQuotationSource,
    ApplicationScenario,
    ApplicationSigningStatus,
    ApplicationStage,
    ApplicationStatus,
    AppointmentChangedVehicleKind,
    AspectRatio,
    AssetCondition,
    AttendeeType,
    AuditTrailKind,
    AutoplayApiVersion,
    BalloonBasedOn,
    BankDisplayInSharePdf,
    BankIntegrationProvider,
    BankKind,
    BannerTextPosition,
    BlockType,
    BodyType,
    BookingPeriodType,
    CalculationMode,
    CalculationRounding,
    CalendarEventAttendeeRole,
    CalendarEventAttendeeStatus,
    CalendarEventEmailType,
    CalendarEventStatus,
    CalendarEventType,
    CapMetadataKind,
    ChangeKind,
    CoeCategory,
    ComboType,
    CompanyTheme,
    ConditionType,
    ConsentFeatureType,
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    CounterMethod,
    CustomerKind,
    DataField,
    DateTimeUnit,
    DayOfWeek,
    DbsPayloadScheme,
    DealershipSettingType,
    DeriveMethod,
    DiscountType,
    DisplayPreference,
    DrivingLicenseType,
    Education,
    EmailProvider,
    Emirate,
    EmploymentStatus,
    EnergyUnit,
    EngineType,
    FieldDisplaySetting,
    FinanceProductBasedOn,
    FinanceProductKind,
    FinanceProductType,
    FinancingPreferenceValue,
    FinderVehicleCondition,
    FinderVehicleFunctionality,
    FinderVehicleStatus,
    GiftVoucherStatus,
    IncomeType,
    InsuranceProductKind,
    InsuranceProductType,
    InsurerIntegrationProvider,
    IntentType,
    InterestRateTableBasedOn,
    InventoryKind,
    JobTitle,
    JobTitleTh,
    KycFieldPurpose,
    LanguageOrientation,
    LayoutType,
    LeadStageOption,
    LeadStatus,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    LTACoeCategory,
    LTAPreOwnerIdType,
    MaskDirection,
    MaybankEnvironment,
    MFASettingsType,
    MileageUnit,
    MobilityBookingLocationType,
    MobilityKind,
    ModuleType,
    MyInfoSettingVersion,
    OFRGeneralFieldKey,
    OFRStatus,
    OnlineOrderableState,
    OptionKind,
    OptionType,
    OrganizerType,
    PackageKind,
    PasswordConfiguration,
    PaymentMode,
    PaymentStatus,
    PreferenceValue,
    PromoTypeEnum,
    PurchaseIntention,
    PurposeOfVisit,
    ReferenceDetailRelationship,
    RelatedEntityType,
    RelationshipApplicant,
    ReservationStockStatus,
    ResidenceType,
    SalesControlBoardDataType,
    SalesFilter,
    SalesOfferAgreementKind,
    SalesOfferDepositMethod,
    SalesOfferDocumentKind,
    SalesOfferDocumentStatus,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
    SettlementInstalmentOn,
    SmsProvider,
    SortOption,
    StockInventoryKind,
    TemplateType,
    TradeInStatus,
    TransmissionKind,
    TransportProtocolType,
    TtbPaymentFlag3ds,
    VehicleKind,
    VehicleType,
    WebPageBlockBackground,
    WebPageBlockPosition,
    WebPageBlockType,
} from '../../database/documents';

/* Sorting order */
export enum SortingOrder {
    Asc = 'asc',
    Desc = 'desc',
}

/* Sorting fields for users */
export enum UserSortingField {
    Company = 'company',
    Email = 'email',
    Authenticator = 'authenticator',
    DisplayName = 'displayName',
    Mobile = 'mobile',
    Role = 'role',
    LastSignedIn = 'lastSignedIn',
}

/* Sorting fields for roles */
export enum RoleSortingField {
    DisplayName = 'displayName',
    UserCount = 'userCount',
    Description = 'description',
}

/* Sorting fields for user groups */
export enum UserGroupSortingField {
    DisplayName = 'displayName',
    UserCount = 'userCount',
}

/* Sorting fields for companies */
export enum CompanySortingField {
    Name = 'name',
    Currency = 'currency',
}

/* Sorting fields for campaigns */
export enum CampaignSortingField {
    CampaignId = 'campaignId',
    Description = 'description',
    IsActive = 'isActive',
    CreatedAt = 'createdAt',
    UpdatedAt = 'updatedAt',
}

/* Sorting fields for C&D */
export enum ConsentsAndDeclarationsSortingField {
    Company = 'company',
    DisplayName = 'displayName',
    Active = 'isActive',
    Type = 'type',
    Mandatory = 'isMandatory',
    OrderNumber = 'orderNumber',
}

export enum LocalVariantSortingField {
    Identifier = 'identifier',
    Company = 'company',
    Name = 'variantName',
    ModelId = 'modelId',
    Order = 'orderNumber',
    IsActive = 'isActive',
    ModelName = 'modelName',
    SubmodelName = 'submodelName',
    Price = 'price',
    ModelOrderAndVariantOrder = 'modelOrderAndVariantOrder',
    HasVariantImage = 'hasVariantImage',
}

export enum VariantAsset {
    Picture = 'picture',
    Brochure = 'brochure',
}

export enum LocalModelSortingField {
    Company = 'company',
    Identifier = 'identifier',
    Name = 'modelName',
    Submodels = 'submodels',
    Variants = 'variants',
    Order = 'orderNumber',
    IsActive = 'isActive',
    MakeName = 'makeName',
    ParentModelName = 'parentModelName',
    BodyType = 'bodyType',
}

export enum LocalMakeSortingField {
    Company = 'company',
    Name = 'makeName',
    Identifier = 'identifier',
    Order = 'orderNumber',
    IsActive = 'isActive',
}

export enum FilterRuleType {
    LocalVariantFilteringRule = 'localVariantFilteringRule',
    LocalModelFilteringRule = 'localModelFilteringRule',
    LocalMakeFilteringRule = 'localMakeFilteringRule',
}

export enum SortingRuleType {
    LocalVariantSortingRule = 'localVariantSortingRule',
    LocalModelSortingRule = 'localModelSortingRule',
    LocalMakeSortingRule = 'localMakeSortingRule',
}

export enum SettingType {
    LocalVariantSettings = 'localVariantSettings',
    LocalModelSettings = 'localModelSettings',
    LocalMakeSettings = 'localMakeSettings',
}

/* Company asset type */
export enum CompanyAsset {
    Logo = 'logo',
    LogoNonWhiteBackground = 'logoNonWhiteBackground',
    MobileLogo = 'mobileLogo',
    Favicon = 'favicon',
    Font = 'font',
    FontBold = 'fontBold',
    EdmEmailIcon = 'edmEmailFooter.emailIcon',
    EdmEmailArrowIcon = 'edmEmailFooter.arrowIcon',
    EdmEmailPhoneIcon = 'edmEmailFooter.phoneIcon',
    Icon = 'icon',
}

/* User asset type */
export enum UserAsset {
    ProfileImage = 'profileImage',
}

/* Sorting fields for banks */
export enum BankSortingField {
    DisplayName = 'displayName',
    CompanyDisplayName = 'companyDisplayName',
    Order = 'order',
}

export enum InsurerSortingField {
    DisplayName = 'displayName',
    CompanyDisplayName = 'companyDisplayName',
    Order = 'order',
}

/* Bank asset type */
export enum BankAsset {
    Logo = 'logo',
    Font = 'font',
    EnbdStampRight = 'enbdStampRight',
    EnbdStampBottom = 'enbdStampBottom',
}

/* Module roles */
export enum ModuleRole {
    Banking = 'banking',
    Signing = 'signing',
    EventApplication = 'eventApplication',
    Payment = 'payment',
    MyInfo = 'myinfo',
    PromoCode = 'promoCode',
    Application = 'application',
    Lead = 'lead',
    Mobility = 'mobility',
    Insurance = 'insurance',
    TradeIn = 'tradeIn',
}

/* Sorting field for finance product */
export enum FinanceProductSortingField {
    Company = 'company',
    Identifier = 'identifier',
    DisplayName = 'displayName',
    FinanceProductType = 'financeProductType',
    Bank = 'bank',
    StartDate = 'startDate',
    EndDate = 'endDate',
    Order = 'order',
}

/* Sorting field for Application */
export enum ApplicationSortingField {
    Vehicle = 'vehicle',
    Bank = 'bank',
    FinanceProduct = 'financeProduct',
    ApplicationStatus = 'status',
    Identifier = 'identifier',
    ApplicationDate = 'applicationDate',
    Assignee = 'assignee',
    TransactionId = 'transactionId',
    LastActivity = 'lastActivity',
    Module = 'module',
    BookingStartDate = 'bookingStartDate',
    BookingEndDate = 'bookingEndDate',
    BookingSubmitted = 'bookingSubmitted',
    Customer = 'customer',
    BusinessPartnerId = 'businessPartnerId',
    VehicleName = 'vehicleName',
    BookingLocation = 'bookingLocation',
    TotalAmountPaid = 'totalAmountPaid',
    Company = 'company',
    AppointmentDate = 'appointmentDate',
    Insurer = 'insurer',
    InsurancePremium = 'insurancePremium',
    FinderVin = 'finderVin',
    CapStatus = 'capStatus',
}

export enum LanguagePackSortingField {
    ReferenceName = 'referenceName',
    DisplayName = 'displayName',
    InternationalCode = 'code',
}

export enum EventLeadOriginType {
    Dealer = 'dealer',
    Internet = 'internet',
}

export enum EventMediumType {
    Walkin = 'walkin',
    Internet = 'internet',
    Event = 'event',
}

export enum EventSortingField {
    /** Sort by Assigned */
    Assigned = 'assigned',
    /** Sort by EventId */
    EventId = 'eventId',
    /** Sort by EventName */
    EventName = 'eventName',
    /** Sort by Payment */
    Payment = 'payment',
    /** Sort by Company */
    Company = 'company',
    /** Sort by Event Module Name */
    ModuleName = 'moduleName',
}

/* Envrionment for Adyen Payment */
export enum Environment {
    Test = 'test',
    Live = 'live',
    PreProduction = 'preproduction',
    Production = 'production',
}

export enum DealerAsset {
    Icon = 'icon',
}

export enum DealerSortingField {
    DisplayName = 'displayName',
    UserCount = 'userCount',
}

/* Sorting field for Customer */
export enum CustomerSortingField {
    Company = 'company',
    FullName = 'fullName',
    Dealer = 'dealer',
    AssigneeName = 'assigneeName',
    ApplicationIdentifier = 'applicationIdentifier',
    LeadIdentifier = 'leadIdentifier',
    ApplicationStatus = 'applicationStatus',
    LeadStatus = 'leadStatus',
    ApplicationModule = 'applicationModule',
    LeadModule = 'leadModule',
    LastActivity = 'lastActivity',
    CreationDate = 'creationDate',
    CustomerCiamId = 'customerCiamId',
    BusinessPartnerId = 'businessPartnerId',
}

export enum ModelConfiguratorAsset {
    Banner = 'banner',
    mobileBanner = 'mobileBanner',
}

export enum VariantConfiguratorAsset {
    PackageSectionImage = 'packageSectionImage',
    OptionsSectionImages = 'optionsSectionImages',
    OptionsSettingImage = 'optionsSettingImage',
    AdditionalDetailsImage = 'additionalDetailsImage',
    ColorSettingImage = 'colorSettingImage',
    TrimSettingImage = 'trimSettingImage',
}

export enum MatrixAsset {
    ColorImage = 'colorImage',
    TrimImage = 'trimImage',
}

export enum ModelConfiguratorSortingField {
    Company = 'company',
    DisplayName = 'displayName',
    VariantConfiguratorCount = 'variantConfiguratorCount',
    VehicleModuleDisplayName = 'vehicleModuleDisplayName',
    ConfiguratorModuleDisplayName = 'configuratorModuleDisplayName',
    VehiclePrice = 'vehiclePrice',
}

export enum VariantConfiguratorSortingField {
    DisplayName = 'displayName',
}

export enum ModuleAsset {
    Image = 'image',
}

export enum PromoCodeSortingField {
    Company = 'company',
    PromoCode = 'promoCode',
    IsActive = 'isActive',
    PromoType = 'promoType',
}

export enum InventorySortingField {
    ID = 'id',
    Company = 'company',
    ModelName = 'modelName',
    SubModelName = 'subModelName',
    VariantName = 'variantName',
    ConfiguratorColorName = 'configuratorColorName',
    ConfiguratorTrimName = 'configuratorTrimName',
    ConfiguratorPackageName = 'configuratorPackageName',
    Vin = 'vin',
    Stock = 'stock',
    ModuleName = 'moduleName',
    AssignedVinCount = 'assignedVinCount',
}

export enum ConfiguratorModuleAsset {
    SaveOrderBanner = 'configuratorSaveOrderBanner',
    SaveOrderIntro = 'configuratorSaveOrderIntro',
    SubmitOrderIntro = 'configuratorSubmitOrderIntro',
}

export enum EventModuleAsset {
    SubmitOrderIntro = 'eventSubmitOrderIntro',
    SubmitOrderIntroScenario = 'eventSubmitOrderIntroScenario',
    CustomerSubmitConfirmation = 'customerSubmitConfirmation',
    CustomerEndTestDriveWithProcess = 'customerEndTestDriveWithProcess',
    CustomerCompleteTestDriveWithoutProcess = 'customerCompleteTestDriveWithoutProcess',
    CustomerBookingAmendment = 'customerBookingAmendment',
    CustomerBookingConfirmation = 'customerBookingConfirmation',
    CustomerBookingCancellation = 'customerBookingCancellation',
}

export enum AppointmentModuleAsset {
    CustomerSubmitConfirmation = 'customerSubmitConfirmation',
    CustomerEndTestDriveWithProcess = 'customerEndTestDriveWithProcess',
    CustomerCompleteTestDriveWithoutProcess = 'customerCompleteTestDriveWithoutProcess',
    CustomerBookingAmendment = 'customerBookingAmendment',
    CustomerBookingConfirmation = 'customerBookingConfirmation',
    CustomerBookingCancellation = 'customerBookingCancellation',
    SalesPersonSubmitConfirmation = 'salesPersonSubmitConfirmation',
    SalesPersonBookingAmendment = 'salesPersonBookingAmendment',
    SalesPersonBookingConfirmation = 'salesPersonBookingConfirmation',
    SalesPersonBookingCancellation = 'salesPersonBookingCancellation',
    SalesPersonFinderReservation = 'salesPersonFinderReservation',
    SalesPersonEndTestDriveReminder = 'salesPersonEndTestDriveReminder',
}
export enum VisitAppointmentModuleAsset {
    CustomerSubmitConfirmation = 'customerSubmitConfirmation',
    CustomerBookingAmendment = 'customerBookingAmendment',
    CustomerBookingConfirmation = 'customerBookingConfirmation',
    CustomerBookingCancellation = 'customerBookingCancellation',
    CustomerBookingComplete = 'customerBookingComplete',
    SalesPersonSubmitConfirmation = 'salesPersonSubmitConfirmation',
    SalesPersonBookingCancellation = 'salesPersonBookingCancellation',
    SalesPersonBookingAmendment = 'salesPersonBookingAmendment',
    SalesPersonBookingConfirmation = 'salesPersonBookingConfirmation',
}

export enum StandardApplicationModuleAsset {
    CustomerShare = 'customerShare',
    CustomerComparisonShare = 'customerComparisonShare',
    CustomerProceedWithCustomerDevice = 'customerProceedWithCustomerDevice',
    CustomerSubmissionConfirmation = 'customerSubmissionConfirmation',
    CustomerApproved = 'customerApproved',
    CustomerRejected = 'customerRejected',
    CustomerCancelled = 'customerCancelled',
    SalesPersonApproved = 'salesPersonApproved',
    SalesPersonRejected = 'salesPersonRejected',
    SalesPersonCancelled = 'salesPersonCancelled',
}

export enum Timeframe {
    CurrentMonth = 'CurrentMonth',
    LastMonth = 'LastMonth',
    Last3Months = 'Last3Months',
    Last6Months = 'Last6Months',
    Last12Months = 'Last12Months',
}

export enum ModuleSortingField {
    DisplayName = 'displayName',
    ModuleType = 'moduleType',
    CompanyName = 'companyName',
}

export enum WebPageSortingField {
    Company = 'company',
    Date = 'date',
    isActive = 'isActive',
    PageTitle = 'pageTitle',
}

export enum WebPageAsset {
    TypeImage = 'typeImage',
    Image = 'image',
    ImageDescription = 'imageDescription',
    Column = 'column',
    Details = 'details',
    MobileDetails = 'mobileDetails',
}

export enum BannerAsset {
    ConfiguratorBanner = 'configuratorBanner',
    ConfiguratorMobileBanner = 'configuratorMobileBanner',
    EventBanner = 'eventBanner',
    EventMobileBanner = 'eventMobileBanner',
}

export enum MobilitySortingField {
    Company = 'company',
    Kind = 'kind',
    ReferenceName = 'referenceName',
    Title = 'title',
    Active = 'isActive',
    Order = 'order',
}

export enum StockSortingField {
    ModelOrderAndVariantOrder = 'modelOrderAndVariantOrder',
    VariantOrder = 'variantOrder',
}

export enum MobilityAsset {
    BookingAmendment = 'bookingAmendment',
    BookingCancellation = 'bookingCancellation',
    BookingConfirmation = 'bookingConfirmation',
    BookingReminder = 'bookingReminder',
    BookingCheckedIn = 'bookingCheckedIn',
    BookingComplete = 'bookingComplete',
}

export enum MobilityRecipient {
    Customers = 'customers',
    Operators = 'operators',
}

export enum MobilityEmailAssetType {
    IntroImage = 'introImage',
}

export enum FinderApplicationModuleAsset {
    SubmitOrderIntro = 'finderApplicationSubmitOrderIntro',
    ReminderEmailIntro = 'finderApplicationReminderEmailIntro',
}

export enum FinderVehicleSortingField {
    Company = 'company',
    InventoryId = 'inventoryId',
    VehicleId = 'vehicleId',
    Name = 'name',
    Condition = 'condition',
    DealerName = 'dealerName',
    ExteriorColor = 'exteriorColor',
    Price = 'price',
    IsInspected = 'isInspected',
    HasLta = 'hasLta',
    IsStateHidden = 'isStateHidden',
    Functionality = 'functionality',
    Status = 'status',
}

export enum FinderVehicleSyncStatus {
    Idle = 'idle',
    Running = 'running',
}

export enum BooleanWithEmpty {
    None = 'none',
    On = 'on',
    Off = 'off',
}

export enum ImportStatus {
    Success = 'success',
    Fail = 'fail',
}

export enum LabelsSortingField {
    Identifier = 'id',
    Company = 'company',
    LabelName = 'labelName',
    TextColor = 'textColor',
    ModuleName = 'moduleName',
}

export enum BannerSortingField {
    Identifier = 'id',
    Company = 'company',
    ModuleName = 'moduleName',
    BannerText = 'bannerText',
}

export enum CompanyFilterListCollection {
    Labels = 'labels',
    PromoCodes = 'promoCodes',
    Configurators = 'configurators',
    Banners = 'banners',
    WebPages = 'webPages',
    Mobilities = 'mobilities',
    Inventories = 'inventories',
    LocalMakes = 'localMakes',
    LocalModels = 'localModels',
    LocalSubmodels = 'localSubmodels',
    LocalVariants = 'localVariants',
    FinderVehicles = 'finderVehicles',
    FinanceProducts = 'financeProducts',
    ConsentsAndDeclarations = 'consentsAndDeclarations',
    Events = 'events',
    InsuranceProducts = 'insuranceProducts',
    OptimumFinanceRenewals = 'optimumFinanceRenewals',
}

export enum ResidentialStatus {
    SelfOwned = 'selfOwned',
    RelativeOwned = 'relativeOwned',
    Rental = 'rental',
    Dormitory = 'dormitory',
    Others = 'others',
}

export enum ApplicationSigningPurpose {
    Finance = 'finance',
    Insurance = 'insurance',
    Mobility = 'mobility',
    TestDrive = 'testDrive',
}

export enum AverageMileageValueUnit {
    Km = 'km',
}

export enum InsuranceProductSortingField {
    Company = 'company',
    Identifier = 'identifier',
    DisplayName = 'displayName',
    Insurer = 'insurer',
    Order = 'order',
    LegalName = 'legalName',
    Description = 'description',
    IsActive = 'isActive',
    StartDate = 'startDate',
    EndDate = 'endDate',
    Type = 'type',
}

export enum LocationSortingField {
    Name = 'name',
}

export enum HomeDeliverySortingField {
    Name = 'name',
}

export enum Purpose {
    Admin = 'admin',
    Production = 'production',
    Default = 'default',
}

export enum GiftVoucherModuleAsset {
    CustomerConfirmation = 'customerConfirmation',
}

export enum GiftVoucherSortingField {
    GiftCode = 'giftCode',
    Value = 'value',
    GiftVoucherStatus = 'status',
    Company = 'company',
    Purchaser = 'purchaser',
    Giftee = 'giftee',
    PurchasedDate = 'purchasedDate',
}

export enum LegalTextPosition {
    Before = 'before',
    After = 'after',
    Modal = 'modal',
}

export enum TradeInSortingField {
    ApplicationDate = 'applicationDate',
    Identifier = 'identifier',
    VehicleNo = 'vehicleNo',
    VehicleMake = 'vehicleMake',
    VehicleModel = 'vehicleModel',
    ManufacturingYear = 'manufacturingYear',
    SelectedDealer = 'selectedDealer',
    OfferPrice = 'offerPrice',
    Status = 'status',
}

export enum ContentRefinementSourceAction {
    Add = 'add',
    Update = 'update',
}

export enum ContentRefinementSourceKind {
    Configurator = 'configurator',
    Module = 'module',
    Event = 'event',
}

export enum EmailContentUpdateType {
    Dealer = 'dealer',
    Module = 'module',
}

export enum CurrentVehicleSource {
    SoldByDealer = 'soldByDealer',
    VehicleInHouseHold = 'vehicleInHouseHold',
    PreviousOwnedVehicle = 'previousOwnedVehicle',
    PurchaseAlternative = 'purchaseAlternative',
    TradeInByDealer = 'tradeInByDealer',
}

export enum CurrentVehicleEquipmentLine {
    Other = 'other',
    Basic = 'basic',
    FullyEquipped = 'fullyEquipped',
    Premium = 'premium',
    Sport = 'sport',
}

export enum ApplicationType {
    StandardApplication = 'StandardApplication',
    EventApplication = 'EventApplication',
    ConfiguratorApplication = 'ConfiguratorApplication',
    MobilityApplication = 'MobilityApplication',
    FinderApplication = 'FinderApplication',
    LaunchpadApplication = 'LaunchpadApplication',
    SalesOfferApplication = 'SalesOfferApplication',
}

export enum LeadType {
    StandardLead = 'StandardLead',
    EventLead = 'EventLead',
    ConfiguratorLead = 'ConfiguratorLead',
    LaunchpadLead = 'LaunchpadLead',
    FinderLead = 'FinderLead',
    MobilityLead = 'MobilityLead',
}

export enum FpTableKind {
    Balloon = 'Balloon',
    InterestRate = 'InterestRate',
    LeasePurchase = 'LeasePurchase',
    LeaseReference = 'LeaseReference',
    ErgoLookup = 'ErgoLookup',
    Deposit = 'Deposit',
    Downpayment = 'Downpayment',
    Displacement = 'Displacement',
    InsuranceFee = 'InsuranceFee',
}

export enum FpTableType {
    Matrix = 'Matrix',
    Default = 'Default',
}

export enum LeadSortingField {
    Vehicle = 'vehicle',
    LeadStatus = 'leadStatus',
    Identifier = 'identifier',
    CreatedAt = 'createdAt',
    UpdatedAt = 'updatedAt',
    Assignee = 'assignee',
    Module = 'module',
    Customer = 'customer',
    BusinessPartnerId = 'businessPartnerId',
    VehicleName = 'vehicleName',
    Company = 'company',
    LeadGenFormCampaignId = 'leadGenFormCampaignId',
    LeadGenFormName = 'leadGenFormName',
}

export enum AddressComponentType {
    ADDRESS = 'ADDRESS',
    SECONDARY_ADDRESS = 'SECONDARY_ADDRESS',
    STREET = 'STREET',
    NEIGHBORHOOD = 'NEIGHBORHOOD',
    LOCALITY = 'LOCALITY',
    DISTRICT = 'DISTRICT',
    REGION = 'REGION',
    POSTCODE = 'POSTCODE',
    COUNTRY = 'COUNTRY',
    PLACE = 'PLACE',
    BLOCK = 'BLOCK',
}

export enum AddressSearchType {
    PARTIAL = 'PARTIAL',
    POSTALCODE = 'POSTALCODE',
}

export enum BusinessPartnerSearchField {
    Email = 'email',
    Phone = 'phone',
    FirstAndLastName = 'firstAndLastName',
    Vin = 'vin',
    PorscheId = 'porscheId',
    FirstAndLastNameJapan = 'firstAndLastNameJapan',
}

export enum CompanyExportFormat {
    EJSON = 'EJSON',
    JSON = 'JSON',
}

export enum OFRModuleAsset {
    CustomerNewOffer = 'customerNewOffer',
    SalesConsultantEquityNotification = 'salesConsultantEquityNotification',
}

export enum OFRSortingField {
    CustomerName = 'customerName',
    Variant = 'variant',
    ModelYear = 'modelYear',
    Bank = 'bank',
    Equity = 'equity',
    Term = 'term',
}
