fragment ApplicationListData on Application {
    id
    ...ApplicationStageData
    ...ApplicationStageUserData

    versioning {
        createdAt
        updatedAt
        createdBy {
            ...AuthorData
        }
        suiteId
    }

    ... on StandardApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        bank {
            id
            displayName
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        module {
            id
            displayName
        }

        dealer {
            id
            displayName
        }

        assignee {
            id
            displayName
        }

        financing {
            ... on DefaultApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on SingaporeApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on NewZealandApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }
        }

        insurancing {
            ... on DefaultApplicationInsurancing {
                insurancePremium
            }

            ... on SingaporeApplicationInsurancing {
                insurancePremium
            }

            ... on NewZealandApplicationInsurancing {
                insurancePremium
            }
        }

        insurer {
            id
            displayName
            legalName {
                ...TranslatedStringData
            }
        }

        financeProduct {
            ... on LocalLease {
                id
                displayName
            }

            ... on LocalLeasePurchase {
                id
                displayName
            }

            ... on LocalHirePurchase {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloon {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloonGFV {
                id
                displayName
            }

            ... on LocalDeferredPrincipal {
                id
                displayName
            }

            ... on LocalUcclLeasing {
                id
                displayName
            }
        }

        deposit {
            amount
            ... on ApplicationAdyenDeposit {
                transactionId
            }

            ... on ApplicationPorscheDeposit {
                transactionId
            }

            ... on ApplicationFiservDeposit {
                transactionId
            }

            ... on ApplicationPayGateDeposit {
                transactionId
            }

            ... on ApplicationTtbDeposit {
                transactionId
            }
        }

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
        }
    }

    ... on EventApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        dealer {
            id
            displayName
        }

        module {
            id
            displayName
        }

        financing {
            ... on DefaultApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on SingaporeApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on NewZealandApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }
        }

        financeProduct {
            ... on LocalLease {
                id
                displayName
            }

            ... on LocalLeasePurchase {
                id
                displayName
            }

            ... on LocalHirePurchase {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloon {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloonGFV {
                id
                displayName
            }

            ... on LocalDeferredPrincipal {
                id
                displayName
            }

            ... on LocalUcclLeasing {
                id
                displayName
            }
        }

        bank {
            id
            displayName
        }

        deposit {
            ... on ApplicationAdyenDeposit {
                transactionId
            }

            ... on ApplicationPorscheDeposit {
                transactionId
            }

            ... on ApplicationFiservDeposit {
                transactionId
            }

            ... on ApplicationPayGateDeposit {
                transactionId
            }

            ... on ApplicationTtbDeposit {
                transactionId
            }
        }

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
        }

        campaignValues {
            capCampaignId
        }

        event {
            id
            displayName
        }
    }

    ... on ConfiguratorApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        bank {
            id
            displayName
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        dealer {
            id
            displayName
        }

        module {
            id
            displayName
        }

        financeProduct {
            ... on LocalLease {
                id
                displayName
            }

            ... on LocalLeasePurchase {
                id
                displayName
            }

            ... on LocalHirePurchase {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloon {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloonGFV {
                id
                displayName
            }

            ... on LocalDeferredPrincipal {
                id
                displayName
            }

            ... on LocalUcclLeasing {
                id
                displayName
            }
        }

        financing {
            ... on DefaultApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on SingaporeApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on NewZealandApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }
        }

        insurancing {
            ... on DefaultApplicationInsurancing {
                insurancePremium
            }

            ... on SingaporeApplicationInsurancing {
                insurancePremium
            }

            ... on NewZealandApplicationInsurancing {
                insurancePremium
            }
        }

        insurer {
            id
            displayName
            legalName {
                ...TranslatedStringData
            }
        }

        deposit {
            ... on ApplicationAdyenDeposit {
                transactionId
            }

            ... on ApplicationPorscheDeposit {
                transactionId
            }

            ... on ApplicationFiservDeposit {
                transactionId
            }

            ... on ApplicationPayGateDeposit {
                transactionId
            }

            ... on ApplicationTtbDeposit {
                transactionId
            }
        }

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
        }
    }

    ... on MobilityApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        module {
            id
            displayName
        }

        dealer {
            id
            displayName
        }

        mobilityBookingDetails {
            inventoryStockPrice

            period {
                ...PeriodData
            }

            location {
                ...MobilityBookingLocationHomeData
                ...MobilityBookingLocationPickupData
            }
        }

        deposit {
            amount
            ... on ApplicationAdyenDeposit {
                transactionId
            }

            ... on ApplicationPorscheDeposit {
                transactionId
            }

            ... on ApplicationFiservDeposit {
                transactionId
            }

            ... on ApplicationPayGateDeposit {
                transactionId
            }

            ... on ApplicationTtbDeposit {
                transactionId
            }
        }

        vin
    }

    ... on FinderApplication {
        applicant {
            ...CustomerSpecs
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        bank {
            id
            displayName
        }

        vehicleId

        dealer {
            id
            displayName
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on FinderVehicle {
                listing {
                    id
                    price {
                        value
                    }
                    vehicle {
                        vin
                    }
                }
            }
        }

        dealer {
            id
            displayName
        }

        module {
            id
            displayName
        }

        financeProduct {
            ... on LocalLease {
                id
                displayName
            }

            ... on LocalLeasePurchase {
                id
                displayName
            }

            ... on LocalHirePurchase {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloon {
                id
                displayName
            }

            ... on LocalHirePurchaseWithBalloonGFV {
                id
                displayName
            }

            ... on LocalDeferredPrincipal {
                id
                displayName
            }

            ... on LocalUcclLeasing {
                id
                displayName
            }
        }

        financing {
            ... on DefaultApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on SingaporeApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }

            ... on NewZealandApplicationFinancing {
                financeProductId
                loan {
                    amount
                }
            }
        }

        insurancing {
            ... on DefaultApplicationInsurancing {
                insurancePremium
            }

            ... on SingaporeApplicationInsurancing {
                insurancePremium
            }

            ... on NewZealandApplicationInsurancing {
                insurancePremium
            }
        }

        insurer {
            id
            displayName
            legalName {
                ...TranslatedStringData
            }
        }

        deposit {
            ... on ApplicationAdyenDeposit {
                transactionId
            }

            ... on ApplicationPorscheDeposit {
                transactionId
            }

            ... on ApplicationFiservDeposit {
                transactionId
            }

            ... on ApplicationPayGateDeposit {
                transactionId
            }

            ... on ApplicationTtbDeposit {
                transactionId
            }
        }

        remarks

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
        }
    }

    ... on LaunchpadApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }
            ... on CorporateCustomer {
                id
                fullName
            }
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        dealer {
            id
            displayName
        }

        module {
            id
            displayName
        }

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
            versioning {
                suiteId
            }
        }

        tradeInVehicle {
            ...TradeInVehicleData
        }

        detailUrl
    }

    ... on SalesOfferApplication {
        applicant {
            ... on LocalCustomer {
                id
                fullName
            }

            ... on CorporateCustomer {
                id
                fullName
            }
        }

        vehicle {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                vehiclePrice
            }
        }

        dealer {
            id
            displayName
        }

        module {
            id
            displayName
        }

        lead {
            identifier
            status
            capValues {
                leadId
                businessPartnerId
            }
            versioning {
                suiteId
            }
        }

        detailUrl
    }

    module {
        company {
            displayName
            legalName {
                ...TranslatedStringData
            }
            timeZone
            currency
            roundings {
                amount {
                    decimals
                }
                percentage {
                    decimals
                }
            }
        }
    }
}
