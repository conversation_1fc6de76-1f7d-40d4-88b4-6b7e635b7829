import type * as SchemaTypes from '../types';

import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { ApplicationStageUserData_ConfiguratorApplication_Fragment, ApplicationStageUserData_EventApplication_Fragment, ApplicationStageUserData_FinderApplication_Fragment, ApplicationStageUserData_LaunchpadApplication_Fragment, ApplicationStageUserData_MobilityApplication_Fragment, ApplicationStageUserData_SalesOfferApplication_Fragment, ApplicationStageUserData_StandardApplication_Fragment } from './ApplicationStageUserData';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { PeriodDataFragment } from './PeriodData';
import type { MobilityBookingLocationHomeDataFragment } from './MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from './MobilityBookingLocationPickupData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import { gql } from '@apollo/client';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { ApplicationStageUserDataFragmentDoc } from './ApplicationStageUserData';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { MobilityBookingLocationHomeDataFragmentDoc } from './MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from './MobilityBookingLocationPickupData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
export type ApplicationListData_ConfiguratorApplication_Fragment = (
  { __typename: 'ConfiguratorApplication' }
  & Pick<SchemaTypes.ConfiguratorApplication, 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName'>
  )>, vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), financeProduct?: SchemaTypes.Maybe<(
    { __typename: 'LocalDeferredPrincipal' }
    & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchase' }
    & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLease' }
    & Pick<SchemaTypes.LocalLease, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'displayName'>
  )>, financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & Pick<SchemaTypes.DefaultApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & Pick<SchemaTypes.NewZealandApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & Pick<SchemaTypes.SingaporeApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  )>, insurancing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationInsurancing' }
    & Pick<SchemaTypes.DefaultApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'NewZealandApplicationInsurancing' }
    & Pick<SchemaTypes.NewZealandApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'SingaporeApplicationInsurancing' }
    & Pick<SchemaTypes.SingaporeApplicationInsurancing, 'insurancePremium'>
  )>, insurer?: SchemaTypes.Maybe<(
    { __typename: 'Insurer' }
    & Pick<SchemaTypes.Insurer, 'id' | 'displayName'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & Pick<SchemaTypes.ApplicationAdyenDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & Pick<SchemaTypes.ApplicationFiservDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & Pick<SchemaTypes.ApplicationPayGateDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & Pick<SchemaTypes.ApplicationPorscheDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & Pick<SchemaTypes.ApplicationTtbDeposit, 'transactionId'>
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_ConfiguratorApplication_Fragment
  & ApplicationStageUserData_ConfiguratorApplication_Fragment
);

export type ApplicationListData_EventApplication_Fragment = (
  { __typename: 'EventApplication' }
  & Pick<SchemaTypes.EventApplication, 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & Pick<SchemaTypes.DefaultApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & Pick<SchemaTypes.NewZealandApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & Pick<SchemaTypes.SingaporeApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  )>, financeProduct?: SchemaTypes.Maybe<(
    { __typename: 'LocalDeferredPrincipal' }
    & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchase' }
    & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLease' }
    & Pick<SchemaTypes.LocalLease, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'displayName'>
  )>, bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName'>
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & Pick<SchemaTypes.ApplicationAdyenDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & Pick<SchemaTypes.ApplicationFiservDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & Pick<SchemaTypes.ApplicationPayGateDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & Pick<SchemaTypes.ApplicationPorscheDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & Pick<SchemaTypes.ApplicationTtbDeposit, 'transactionId'>
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ), campaignValues?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationCampaignValues' }
    & Pick<SchemaTypes.ApplicationCampaignValues, 'capCampaignId'>
  )>, event: (
    { __typename: 'Event' }
    & Pick<SchemaTypes.Event, 'id' | 'displayName'>
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_EventApplication_Fragment
  & ApplicationStageUserData_EventApplication_Fragment
);

export type ApplicationListData_FinderApplication_Fragment = (
  { __typename: 'FinderApplication' }
  & Pick<SchemaTypes.FinderApplication, 'vehicleId' | 'remarks' | 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
    & CustomerSpecs_LocalCustomer_Fragment
  ), bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName'>
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { listing: (
      { __typename: 'Listing' }
      & Pick<SchemaTypes.Listing, 'id'>
      & { price: (
        { __typename: 'Price' }
        & Pick<SchemaTypes.Price, 'value'>
      ), vehicle: (
        { __typename: 'PorscheFinderVehicle' }
        & Pick<SchemaTypes.PorscheFinderVehicle, 'vin'>
      ) }
    ), name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), financeProduct?: SchemaTypes.Maybe<(
    { __typename: 'LocalDeferredPrincipal' }
    & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchase' }
    & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLease' }
    & Pick<SchemaTypes.LocalLease, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'displayName'>
  )>, financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & Pick<SchemaTypes.DefaultApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & Pick<SchemaTypes.NewZealandApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & Pick<SchemaTypes.SingaporeApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  )>, insurancing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationInsurancing' }
    & Pick<SchemaTypes.DefaultApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'NewZealandApplicationInsurancing' }
    & Pick<SchemaTypes.NewZealandApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'SingaporeApplicationInsurancing' }
    & Pick<SchemaTypes.SingaporeApplicationInsurancing, 'insurancePremium'>
  )>, insurer?: SchemaTypes.Maybe<(
    { __typename: 'Insurer' }
    & Pick<SchemaTypes.Insurer, 'id' | 'displayName'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & Pick<SchemaTypes.ApplicationAdyenDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & Pick<SchemaTypes.ApplicationFiservDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & Pick<SchemaTypes.ApplicationPayGateDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & Pick<SchemaTypes.ApplicationPorscheDeposit, 'transactionId'>
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & Pick<SchemaTypes.ApplicationTtbDeposit, 'transactionId'>
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_FinderApplication_Fragment
  & ApplicationStageUserData_FinderApplication_Fragment
);

export type ApplicationListData_LaunchpadApplication_Fragment = (
  { __typename: 'LaunchpadApplication' }
  & Pick<SchemaTypes.LaunchpadApplication, 'detailUrl' | 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ), tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_LaunchpadApplication_Fragment
  & ApplicationStageUserData_LaunchpadApplication_Fragment
);

export type ApplicationListData_MobilityApplication_Fragment = (
  { __typename: 'MobilityApplication' }
  & Pick<SchemaTypes.MobilityApplication, 'vin' | 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), mobilityBookingDetails: (
    { __typename: 'MobilityBookingDetails' }
    & Pick<SchemaTypes.MobilityBookingDetails, 'inventoryStockPrice'>
    & { period: (
      { __typename: 'Period' }
      & PeriodDataFragment
    ), location: (
      { __typename: 'MobilityBookingLocationHome' }
      & MobilityBookingLocationHomeDataFragment
    ) | (
      { __typename: 'MobilityBookingLocationPickup' }
      & MobilityBookingLocationPickupDataFragment
    ) }
  ), deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & Pick<SchemaTypes.ApplicationAdyenDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & Pick<SchemaTypes.ApplicationFiservDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & Pick<SchemaTypes.ApplicationPayGateDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & Pick<SchemaTypes.ApplicationPorscheDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & Pick<SchemaTypes.ApplicationTtbDeposit, 'transactionId' | 'amount'>
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_MobilityApplication_Fragment
  & ApplicationStageUserData_MobilityApplication_Fragment
);

export type ApplicationListData_SalesOfferApplication_Fragment = (
  { __typename: 'SalesOfferApplication' }
  & Pick<SchemaTypes.SalesOfferApplication, 'detailUrl' | 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )>, versioning: (
      { __typename: 'AdvancedVersioning' }
      & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
    ) }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_SalesOfferApplication_Fragment
  & ApplicationStageUserData_SalesOfferApplication_Fragment
);

export type ApplicationListData_StandardApplication_Fragment = (
  { __typename: 'StandardApplication' }
  & Pick<SchemaTypes.StandardApplication, 'id'>
  & { applicant: (
    { __typename: 'CorporateCustomer' }
    & Pick<SchemaTypes.CorporateCustomer, 'id' | 'fullName'>
  ) | { __typename: 'Guarantor' } | (
    { __typename: 'LocalCustomer' }
    & Pick<SchemaTypes.LocalCustomer, 'id' | 'fullName'>
  ), bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'displayName'>
  )>, vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & Pick<SchemaTypes.FinderVehicle, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalMake' }
    & Pick<SchemaTypes.LocalMake, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalModel' }
    & Pick<SchemaTypes.LocalModel, 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  ) | (
    { __typename: 'LocalVariant' }
    & Pick<SchemaTypes.LocalVariant, 'vehiclePrice' | 'id' | 'identifier'>
    & { name: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, module: (
    { __typename: 'AdyenPaymentModule' }
    & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AppointmentModule' }
    & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'AutoplayModule' }
    & Pick<SchemaTypes.AutoplayModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BankModule' }
    & Pick<SchemaTypes.BankModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'BasicSigningModule' }
    & Pick<SchemaTypes.BasicSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CapModule' }
    & Pick<SchemaTypes.CapModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConfiguratorModule' }
    & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'CtsModule' }
    & Pick<SchemaTypes.CtsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'DocusignModule' }
    & Pick<SchemaTypes.DocusignModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'EventApplicationModule' }
    & Pick<SchemaTypes.EventApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'FiservPaymentModule' }
    & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'GiftVoucherModule' }
    & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'InsuranceModule' }
    & Pick<SchemaTypes.InsuranceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LabelsModule' }
    & Pick<SchemaTypes.LabelsModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LaunchPadModule' }
    & Pick<SchemaTypes.LaunchPadModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MaintenanceModule' }
    & Pick<SchemaTypes.MaintenanceModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MarketingModule' }
    & Pick<SchemaTypes.MarketingModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MobilityModule' }
    & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'MyInfoModule' }
    & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'NamirialSigningModule' }
    & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OFRModule' }
    & Pick<SchemaTypes.OfrModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'OIDCModule' }
    & Pick<SchemaTypes.OidcModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheIdModule' }
    & Pick<SchemaTypes.PorscheIdModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorschePaymentModule' }
    & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PorscheRetainModule' }
    & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'PromoCodeModule' }
    & Pick<SchemaTypes.PromoCodeModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SalesOfferModule' }
    & Pick<SchemaTypes.SalesOfferModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'StandardApplicationModule' }
    & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TradeInModule' }
    & Pick<SchemaTypes.TradeInModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentModule' }
    & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WebsiteModule' }
    & Pick<SchemaTypes.WebsiteModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'displayName'>
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'displayName' | 'timeZone' | 'currency'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
  ), dealer: (
    { __typename: 'Dealer' }
    & Pick<SchemaTypes.Dealer, 'id' | 'displayName'>
  ), assignee?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & Pick<SchemaTypes.User, 'id' | 'displayName'>
  )>, financing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationFinancing' }
    & Pick<SchemaTypes.DefaultApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'NewZealandApplicationFinancing' }
    & Pick<SchemaTypes.NewZealandApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  ) | (
    { __typename: 'SingaporeApplicationFinancing' }
    & Pick<SchemaTypes.SingaporeApplicationFinancing, 'financeProductId'>
    & { loan?: SchemaTypes.Maybe<(
      { __typename: 'ApplicationValueSetting' }
      & Pick<SchemaTypes.ApplicationValueSetting, 'amount'>
    )> }
  )>, insurancing?: SchemaTypes.Maybe<(
    { __typename: 'DefaultApplicationInsurancing' }
    & Pick<SchemaTypes.DefaultApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'NewZealandApplicationInsurancing' }
    & Pick<SchemaTypes.NewZealandApplicationInsurancing, 'insurancePremium'>
  ) | (
    { __typename: 'SingaporeApplicationInsurancing' }
    & Pick<SchemaTypes.SingaporeApplicationInsurancing, 'insurancePremium'>
  )>, insurer?: SchemaTypes.Maybe<(
    { __typename: 'Insurer' }
    & Pick<SchemaTypes.Insurer, 'id' | 'displayName'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, financeProduct?: SchemaTypes.Maybe<(
    { __typename: 'LocalDeferredPrincipal' }
    & Pick<SchemaTypes.LocalDeferredPrincipal, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchase' }
    & Pick<SchemaTypes.LocalHirePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloon' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloon, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalHirePurchaseWithBalloonGFV' }
    & Pick<SchemaTypes.LocalHirePurchaseWithBalloonGfv, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLease' }
    & Pick<SchemaTypes.LocalLease, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalLeasePurchase' }
    & Pick<SchemaTypes.LocalLeasePurchase, 'id' | 'displayName'>
  ) | (
    { __typename: 'LocalUcclLeasing' }
    & Pick<SchemaTypes.LocalUcclLeasing, 'id' | 'displayName'>
  )>, deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & Pick<SchemaTypes.ApplicationAdyenDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & Pick<SchemaTypes.ApplicationFiservDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & Pick<SchemaTypes.ApplicationPayGateDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & Pick<SchemaTypes.ApplicationPorscheDeposit, 'transactionId' | 'amount'>
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & Pick<SchemaTypes.ApplicationTtbDeposit, 'transactionId' | 'amount'>
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & Pick<SchemaTypes.ConfiguratorLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'EventLead' }
    & Pick<SchemaTypes.EventLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'FinderLead' }
    & Pick<SchemaTypes.FinderLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'LaunchpadLead' }
    & Pick<SchemaTypes.LaunchpadLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'MobilityLead' }
    & Pick<SchemaTypes.MobilityLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ) | (
    { __typename: 'StandardLead' }
    & Pick<SchemaTypes.StandardLead, 'identifier' | 'status'>
    & { capValues?: SchemaTypes.Maybe<(
      { __typename: 'CapValuesOnApplication' }
      & Pick<SchemaTypes.CapValuesOnApplication, 'leadId' | 'businessPartnerId'>
    )> }
  ), versioning: (
    { __typename: 'AdvancedVersioning' }
    & Pick<SchemaTypes.AdvancedVersioning, 'createdAt' | 'updatedAt' | 'suiteId'>
    & { createdBy?: SchemaTypes.Maybe<(
      { __typename: 'CorporateCustomer' }
      & AuthorData_CorporateCustomer_Fragment
    ) | (
      { __typename: 'ExternalBank' }
      & AuthorData_ExternalBank_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & AuthorData_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & AuthorData_LocalCustomer_Fragment
    ) | (
      { __typename: 'PorscheRetain' }
      & AuthorData_PorscheRetain_Fragment
    ) | (
      { __typename: 'Salesforce' }
      & AuthorData_Salesforce_Fragment
    ) | (
      { __typename: 'SystemBank' }
      & AuthorData_SystemBank_Fragment
    ) | (
      { __typename: 'User' }
      & AuthorData_User_Fragment
    )> }
  ) }
  & ApplicationStageData_StandardApplication_Fragment
  & ApplicationStageUserData_StandardApplication_Fragment
);

export type ApplicationListDataFragment = ApplicationListData_ConfiguratorApplication_Fragment | ApplicationListData_EventApplication_Fragment | ApplicationListData_FinderApplication_Fragment | ApplicationListData_LaunchpadApplication_Fragment | ApplicationListData_MobilityApplication_Fragment | ApplicationListData_SalesOfferApplication_Fragment | ApplicationListData_StandardApplication_Fragment;

export const ApplicationListDataFragmentDoc = /*#__PURE__*/ gql`
    fragment ApplicationListData on Application {
  id
  ...ApplicationStageData
  ...ApplicationStageUserData
  versioning {
    createdAt
    updatedAt
    createdBy {
      ...AuthorData
    }
    suiteId
  }
  ... on StandardApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    bank {
      id
      displayName
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    module {
      id
      displayName
    }
    dealer {
      id
      displayName
    }
    assignee {
      id
      displayName
    }
    financing {
      ... on DefaultApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on SingaporeApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on NewZealandApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
    }
    insurancing {
      ... on DefaultApplicationInsurancing {
        insurancePremium
      }
      ... on SingaporeApplicationInsurancing {
        insurancePremium
      }
      ... on NewZealandApplicationInsurancing {
        insurancePremium
      }
    }
    insurer {
      id
      displayName
      legalName {
        ...TranslatedStringData
      }
    }
    financeProduct {
      ... on LocalLease {
        id
        displayName
      }
      ... on LocalLeasePurchase {
        id
        displayName
      }
      ... on LocalHirePurchase {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloon {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloonGFV {
        id
        displayName
      }
      ... on LocalDeferredPrincipal {
        id
        displayName
      }
      ... on LocalUcclLeasing {
        id
        displayName
      }
    }
    deposit {
      amount
      ... on ApplicationAdyenDeposit {
        transactionId
      }
      ... on ApplicationPorscheDeposit {
        transactionId
      }
      ... on ApplicationFiservDeposit {
        transactionId
      }
      ... on ApplicationPayGateDeposit {
        transactionId
      }
      ... on ApplicationTtbDeposit {
        transactionId
      }
    }
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
    }
  }
  ... on EventApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    dealer {
      id
      displayName
    }
    module {
      id
      displayName
    }
    financing {
      ... on DefaultApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on SingaporeApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on NewZealandApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
    }
    financeProduct {
      ... on LocalLease {
        id
        displayName
      }
      ... on LocalLeasePurchase {
        id
        displayName
      }
      ... on LocalHirePurchase {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloon {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloonGFV {
        id
        displayName
      }
      ... on LocalDeferredPrincipal {
        id
        displayName
      }
      ... on LocalUcclLeasing {
        id
        displayName
      }
    }
    bank {
      id
      displayName
    }
    deposit {
      ... on ApplicationAdyenDeposit {
        transactionId
      }
      ... on ApplicationPorscheDeposit {
        transactionId
      }
      ... on ApplicationFiservDeposit {
        transactionId
      }
      ... on ApplicationPayGateDeposit {
        transactionId
      }
      ... on ApplicationTtbDeposit {
        transactionId
      }
    }
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
    }
    campaignValues {
      capCampaignId
    }
    event {
      id
      displayName
    }
  }
  ... on ConfiguratorApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    bank {
      id
      displayName
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    dealer {
      id
      displayName
    }
    module {
      id
      displayName
    }
    financeProduct {
      ... on LocalLease {
        id
        displayName
      }
      ... on LocalLeasePurchase {
        id
        displayName
      }
      ... on LocalHirePurchase {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloon {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloonGFV {
        id
        displayName
      }
      ... on LocalDeferredPrincipal {
        id
        displayName
      }
      ... on LocalUcclLeasing {
        id
        displayName
      }
    }
    financing {
      ... on DefaultApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on SingaporeApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on NewZealandApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
    }
    insurancing {
      ... on DefaultApplicationInsurancing {
        insurancePremium
      }
      ... on SingaporeApplicationInsurancing {
        insurancePremium
      }
      ... on NewZealandApplicationInsurancing {
        insurancePremium
      }
    }
    insurer {
      id
      displayName
      legalName {
        ...TranslatedStringData
      }
    }
    deposit {
      ... on ApplicationAdyenDeposit {
        transactionId
      }
      ... on ApplicationPorscheDeposit {
        transactionId
      }
      ... on ApplicationFiservDeposit {
        transactionId
      }
      ... on ApplicationPayGateDeposit {
        transactionId
      }
      ... on ApplicationTtbDeposit {
        transactionId
      }
    }
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
    }
  }
  ... on MobilityApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    module {
      id
      displayName
    }
    dealer {
      id
      displayName
    }
    mobilityBookingDetails {
      inventoryStockPrice
      period {
        ...PeriodData
      }
      location {
        ...MobilityBookingLocationHomeData
        ...MobilityBookingLocationPickupData
      }
    }
    deposit {
      amount
      ... on ApplicationAdyenDeposit {
        transactionId
      }
      ... on ApplicationPorscheDeposit {
        transactionId
      }
      ... on ApplicationFiservDeposit {
        transactionId
      }
      ... on ApplicationPayGateDeposit {
        transactionId
      }
      ... on ApplicationTtbDeposit {
        transactionId
      }
    }
    vin
  }
  ... on FinderApplication {
    applicant {
      ...CustomerSpecs
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    bank {
      id
      displayName
    }
    vehicleId
    dealer {
      id
      displayName
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on FinderVehicle {
        listing {
          id
          price {
            value
          }
          vehicle {
            vin
          }
        }
      }
    }
    dealer {
      id
      displayName
    }
    module {
      id
      displayName
    }
    financeProduct {
      ... on LocalLease {
        id
        displayName
      }
      ... on LocalLeasePurchase {
        id
        displayName
      }
      ... on LocalHirePurchase {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloon {
        id
        displayName
      }
      ... on LocalHirePurchaseWithBalloonGFV {
        id
        displayName
      }
      ... on LocalDeferredPrincipal {
        id
        displayName
      }
      ... on LocalUcclLeasing {
        id
        displayName
      }
    }
    financing {
      ... on DefaultApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on SingaporeApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
      ... on NewZealandApplicationFinancing {
        financeProductId
        loan {
          amount
        }
      }
    }
    insurancing {
      ... on DefaultApplicationInsurancing {
        insurancePremium
      }
      ... on SingaporeApplicationInsurancing {
        insurancePremium
      }
      ... on NewZealandApplicationInsurancing {
        insurancePremium
      }
    }
    insurer {
      id
      displayName
      legalName {
        ...TranslatedStringData
      }
    }
    deposit {
      ... on ApplicationAdyenDeposit {
        transactionId
      }
      ... on ApplicationPorscheDeposit {
        transactionId
      }
      ... on ApplicationFiservDeposit {
        transactionId
      }
      ... on ApplicationPayGateDeposit {
        transactionId
      }
      ... on ApplicationTtbDeposit {
        transactionId
      }
    }
    remarks
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
    }
  }
  ... on LaunchpadApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    dealer {
      id
      displayName
    }
    module {
      id
      displayName
    }
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
      versioning {
        suiteId
      }
    }
    tradeInVehicle {
      ...TradeInVehicleData
    }
    detailUrl
  }
  ... on SalesOfferApplication {
    applicant {
      ... on LocalCustomer {
        id
        fullName
      }
      ... on CorporateCustomer {
        id
        fullName
      }
    }
    vehicle {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        vehiclePrice
      }
    }
    dealer {
      id
      displayName
    }
    module {
      id
      displayName
    }
    lead {
      identifier
      status
      capValues {
        leadId
        businessPartnerId
      }
      versioning {
        suiteId
      }
    }
    detailUrl
  }
  module {
    company {
      displayName
      legalName {
        ...TranslatedStringData
      }
      timeZone
      currency
      roundings {
        amount {
          decimals
        }
        percentage {
          decimals
        }
      }
    }
  }
}
    `;