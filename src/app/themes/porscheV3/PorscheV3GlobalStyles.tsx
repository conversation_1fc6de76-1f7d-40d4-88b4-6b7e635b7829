import { useEffect } from 'react';
import { createGlobalStyle, css } from 'styled-components';
import { LayoutType } from '../../api/types';
import breakpoints from '../../utilities/breakpoints';
import { FooterGlobalStyle, AntFormItemGlobalStyle } from '../GlobalStyles';
import NotificationGlobalStyle from '../porsche/notification/NotificationGlobalStyle';
import { PORSCHE_V3_THEME_CLASSNAME } from '../shared';
import themeTokens from './themeTokens';

const GlobalThemeToken = createGlobalStyle`
    html.${PORSCHE_V3_THEME_CLASSNAME} {
        /* For css variables antd override */
        --ant-primary-color: ${themeTokens.primaryColor};
        --ant-primary-color-hover: ${themeTokens.hoveredPrimaryColor};
        --ant-primary-color-active: ${themeTokens.primaryColor};

        --font-family: ${themeTokens.fontFamily};

        /* font sizes */
        --button-font-size: ${themeTokens.buttonFontSize};
        --input-font-size: ${themeTokens.inputFontSize};

        /* input field */
        --input-height: ${themeTokens.inputFieldHeight};

        /* checkboxes */
        --checkbox-width: 1.5rem;
        --checkbox-height: 1.5rem;

        --checkbox-inner-border-color: #ffffff;
        --checkbox-inner-background-color: #626669;

        --checkbox-summary-checked-inner-border-color: #323639;
        --checkbox-checked-inner-border-color: #323639;
        --checkbox-summary-checked-inner-background-color: #323639;
        --checkbox-checked-inner-background-color: #323639;

        --checkbox-checked-inner-after-border-color: #ffffff;

        /* For configurator pages */
        --configurator-text-color: ${themeTokens.textColor};
        --configurator-border-radius: 0;
        --configurator-divider-color: #00000020;
        --configurator-calculator-pre-summary-border-color: transparent;
        --configurator-calculator-summary-border-color: transparent;

        --configurator-back-color: ${themeTokens.primaryColor};
        --configurator-back-color-hover: ${themeTokens.hoveredPrimaryColor};
        --configurator-back-color-active: ${themeTokens.primaryColor};

        --configurator-widget-active-color: var(--ant-primary-color);
        --configurator-widget-title-font-weight: 900;
        --configurator-widget-background: #eeeff2;
        --configurator-widget-text-color: ${themeTokens.textColor};

        --configurator-widget-popup-background: #eeeff2;
        --configurator-widget-popup-summary-text-color: ${themeTokens.textColor};
        --configurator-widget-popup-text-color: ${themeTokens.textColor};
        --configurator-widget-popup-disabled-text-color: rgb(148, 149, 152);
        --configurator-widget-popup-text-color-opacity: 1;
        --configurator-widget-popup-calculator-background: #00000011;

        --configurator-widget-bar-color: #b5b5b5;
        --configurator-widget-bar-open-color: #b5b5b5;

        --configurator-widget-checked-color: #ffffff;

        --configurator-vehicle-select-height: 82px;
        --configurator-vehicle-select-label-padding-bottom: 0px;
        --configurator-widget-popup-calculator-field-background-color: transparent;

        --configurator-widget-dealer-select-container-height: auto;
        --configurator-widget-dealer-select-container-form-item-display: block;


        .ant-pro-footer-bar {
            padding-top: 8px;
            padding-bottom: 8px;
            padding-right: 60px;
            @media (max-width: ${breakpoints.md}) {
                padding-right: 24px;
            };

            ${props =>
                props?.theme?.layoutType === LayoutType.PorscheV3 &&
                css`
                    margin: 0 24px 36px;
                    width: calc(100% - 48px) !important;
                    border-radius: 8px;
                    padding: 16px;
                    background-color: #535457;

                    @media (max-width: ${breakpoints.md}) {
                        margin: 0;
                        border-radius: 0px;
                        width: 100% !important;
                    }
                `}
        }

        .journey-toolbar {
            position: relative;
            margin: 0;
            top: 64px;
            border-radius: 0px;
            background-color: #FFFFFF;
            padding: 16px 16px;
            width: calc(100% + 16px) !important;
            box-shadow: rgb(255, 255, 255) calc(clamp(16px, 12px + 1.25vw, 24px) * -1) 1px 0px 0px, rgb(255, 255, 255) calc(clamp(16px, 12px + 1.25vw, 24px)) 1px 0px 0px;

            @media (min-width: ${breakpoints.xl}){
                padding: 16px 24px;
            }
        }

        /* For calculator ui */
        --calculator-form-item-error-border: 0;

        --calculator-field-background-color: transparent;
        --calculator-field-label-padding: 0;
        --calculator-field-label-color: rgb(1, 2, 5);
        --calculator-field-disabled-label-color: rgb(148, 149, 152);
        --calculator-field-padding: 0;
        --calculator-grid-item-gap: 16px;
        --calculator-grid-item-min-height: 54px;

        --calculator-comparison-form-item-height-default: 58px;
        --calculator-comparison-form-item-height-second: 86px;
        --calculator-comparison-below-condition-background: #eeeff2;
        --calculator-comparison-form-item-ant-typography-padding-left-right: 0px;
        --calculator-comparison-mobile-form-item-padding-top: 48px;
        --calculator-comparison-mobile-form-item-padding-top-sm: 58px;
        --calculator-comparison-form-item-error-position: static;
        --calculator-comparison-vehicle-image-height: 333px;
        --calculator-comparison-vehicle-image-height-xl: 175px;
        --calculator-comparison-vehicle-image-height-lg: 117px;
        --calculator-comparison-vehicle-image-height-m: 180px;
        --calculator-comparison-vehicle-image-height-sm: 200px;

        --mobility-rental-field-margin-bottom-inner: 24px;

        --vehicle-image-border-radius: 6px;

        --image-selection-border: 2px solid transparent;
        --image-selection-border-radius: 4px;
        --image-selection-active-border-color: #6b6d70;
        --image-selection-outline: 0px;
        --image-selection-outline-offset: 0px;
        --image-selection-active-outline-color: transparent;

        --carousel-images-dots-bottom: 0;
        --carousel-images-dots-gap: clamp(16px, 12px + 1.25vw, 36px);
        --carousel-images-dots-width: 8px;
        --carousel-images-dots-height: 8px;
        --carousel-images-dots-background-color: #6B6D70;
        --carousel-images-dots-border-radius: 4px;
        --carousel-images-dots-opacity: 1;
        --carousel-images-dots-box-shadow: none;
        --carousel-images-dots-active-width: 20px;
        --carousel-images-dots-active-height: 8px;
        --carousel-images-dots-active-background-color: #010205;
        --carousel-images-dots-active-opacity: 1;
        --carousel-images-dots-active-box-shadow: none;

        --card-border-radius: 4px;
        --datepicker-label-padding-form-item: 0px 0px 4px;

        --copyright-text-color:  #010205;

        /* since p-modal has z-index of 99999 and tooltip's default z-index is 1070, showing tooltip on the p-modal will not be visible. */
        .ant-tooltip {
            z-index: 100000;
        }

        /* since p-modal has z-index of 99999 and ant datepicker dropdown default z-index is 1050, showing ant datepicker dropdown on the p-modal will not be visible. */
        .ant-picker-dropdown {
            z-index: 100000
        }
        
        --pds-gradient-to-top: ${themeTokens.gradientToTop};
        --pds-theme-dark-primary: ${themeTokens.darkPrimaryColor};
        --pds-spacing-fluid-large: ${themeTokens.spacingFluidLarge};
        --pds-spacing-fluid-medium: ${themeTokens.spacingFluidMedium};
        --pds-theme-light-background-surface: ${themeTokens.themeLightBackgroundSurface};

        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3 &&
            css`
                .v3-layout-card,
                .v3-layout-card-small,
                .v3-layout-card-small-booking-deposit {
                    border-radius: 12px;
                    background-color: #fff;
                }

                .v3-layout-card {
                    padding: 24px;

                    @media (min-width: ${breakpoints.md}) {
                        padding: 32px;
                    }
                }

                .v3-layout-card-small {
                    padding: 16px;
                }

                .v3-layout-card-small-booking-deposit {
                    padding: 12px 16px;
                }

                .v3-event-thankyou-booking-deposit {
                    background-color: transparent;
                    padding: 16px 0px 0px 0px;
                }

                /* footer height + margin in V3 layout */
                --v3-layout-footer-height: 150px;
            `}
    }

     .launchpad-modal .ant-form-item {
        margin-bottom: 0px;
     }
`;

const PorscheGlobalStyles = () => {
    useEffect(() => {
        document.documentElement.classList.add(PORSCHE_V3_THEME_CLASSNAME);

        return () => {
            document.documentElement.classList.remove(PORSCHE_V3_THEME_CLASSNAME);
        };
    }, []);

    return (
        <>
            <NotificationGlobalStyle />
            <FooterGlobalStyle />
            <GlobalThemeToken />
            <AntFormItemGlobalStyle />
        </>
    );
};

export default PorscheGlobalStyles;
