import type { InputRef } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { getOr, isEmpty, isNil, pick, sortBy, concat, uniq } from 'lodash/fp';
import type { Key } from 'react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { LeadListDataFragment } from '../../../api/fragments/LeadListData';
import { useGetLeadsListFiltersQuery } from '../../../api/queries/getLeadsListFilter';
import { useListLeadsQuery } from '../../../api/queries/listLeads';
import type { LeadStageOption, LeadStatus } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import type { PaginatedTableProps } from '../../../components/PaginatedTable';
import SearchBox from '../../../components/SearchBox';
import { useCompanies, useCompany } from '../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../themes/hooks';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useFormatDate from '../../../utilities/useFormatDate';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import useTranslatedString from '../../../utilities/useTranslatedString';
import type { EnhancedColumnType } from '../ApplicationList';
import { ApplicationColumns } from '../ApplicationList/helpers';
import { getSortField, getSourceValue, renderStatusTag } from './helpers';
import useListReducer from './useListReducer';
import useSortOrder from './useSortOrder';

export type LeadListProps = {
    dealerIds?: string[];
    eventId?: string;
    finderVehicleVin?: string;
    configuratorId?: string;
    desiredColumns: ApplicationColumns[];
    setSelectedLead?: React.Dispatch<React.SetStateAction<string[]>>;
    setLoadedLeadIds?: React.Dispatch<React.SetStateAction<string[]>>;
    setModuleListByLead?: React.Dispatch<React.SetStateAction<string[]>>;
    onRow?: (isLead: boolean, suiteId: string) => void;
    leadStage: LeadStageOption;
    isAllowSelection?: boolean;
    includeMergedStatus?: boolean;
    forCi?: boolean;
};

type LeadDataSource = LeadListDataFragment & { key: Key };

type PaginatedLeadTable = PaginatedTableProps<LeadDataSource>;

const LeadList = ({
    dealerIds,
    eventId,
    finderVehicleVin,
    desiredColumns,
    configuratorId,
    setSelectedLead,
    onRow,
    setLoadedLeadIds,
    setModuleListByLead,
    leadStage,
    isAllowSelection = true,
    includeMergedStatus = false,
    forCi = false,
}: LeadListProps) => {
    const { PaginatedTableWithContext } = useThemeComponents();
    const company = useCompany(true);
    const companies = useCompanies(true);
    const { t } = useTranslation(['common', 'leadListPage']);
    const formatDate = useFormatDate();
    const translatedString = useTranslatedString();

    const inputRef = useRef<InputRef>(null);
    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Lead);
    const [state, dispatch] = useListReducer(currentCache);
    const { page, pageSize, sort, filter } = state;

    const navigate = useNavigate();

    const { data } = useListLeadsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { dealerIds, eventId, finderVehicleVin, configuratorId, leadStage, includeMergedStatus, ...filter },
        },
        onCompleted(response) {
            if (setLoadedLeadIds) {
                setLoadedLeadIds(currentReservationIds =>
                    uniq(
                        concat(
                            currentReservationIds,
                            concat(
                                currentReservationIds,
                                response.list.items.map(item => item.id)
                            )
                        )
                    )
                );
            }
        },
    });

    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const showSearchDropDown = useCallback(
        (props: FilterDropdownProps, dataIndex: string) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const { data: appFilterList } = useGetLeadsListFiltersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyIds: company ? [company.id] : companies.map(company => company.id),
                includeMergedStatus,
            },
        },
    });

    const [statusList, moduleList, companyList] = useMemo(() => {
        const filterList = appFilterList?.getLeadsListFilters;
        if (!filterList) {
            return [[], [], []];
        }

        const statusList = sortBy(
            'text',
            filterList.leadStatus.map(status => ({
                text: t(`leadListPage:status.${status}`),
                value: status,
            }))
        );

        const moduleList = sortBy(
            'text',
            filterList.modules.map(module => ({
                text: module.displayName,
                value: module.id,
            }))
        );

        const companyList = sortBy(
            'text',
            filterList.companies.map(company => ({
                text: company.displayName,
                value: company.id,
            }))
        );

        return [statusList, moduleList, companyList];
    }, [appFilterList?.getLeadsListFilters, t]);

    useEffect(() => {
        if (setModuleListByLead) {
            const moduleListIds = moduleList.map(module => module.value);
            setModuleListByLead(moduleListIds);
        }
    }, [moduleList, setModuleListByLead]);

    const {
        companySort,
        vehicleNameSort,
        leadGenFormNameSort,
        leadGenFormCampaignIdSort,
        assigneeSort,
        identifierSort,
        customerSort,
        moduleSort,
        appDateSort,
        applicationStatusSort,
        businessPartnerIdSort,
    } = useSortOrder(sort);

    const onChange = useCallback<PaginatedLeadTable['onChange']>(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort': {
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });
                }

                case 'filter': {
                    return dispatch({
                        type: 'setFilter',
                        filteringRule: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            customer: !filters.applicant ? undefined : (filters.applicant[0] as string),
                            businessPartnerId: !filters.businessPartnerId
                                ? undefined
                                : (filters.businessPartnerId[0] as string),
                            vehicle: !filters.vehicle ? undefined : (filters.vehicle[0] as string),
                            identifier: !filters.identifier ? undefined : (filters.identifier[0] as string),
                            moduleIds: !filters.module ? undefined : (filters.module as string[]),
                            assignee: !filters.assignee ? undefined : (filters.assignee[0] as string),
                            status: !filters.status ? undefined : (filters.status as LeadStatus[]),
                            eventDisplayName: !filters.leadGenFormName
                                ? undefined
                                : (filters.leadGenFormName[0] as string),
                            eventCampaignId: !filters.leadGenFormCampaignId
                                ? undefined
                                : (filters.leadGenFormCampaignId[0] as string),
                        },
                    });
                }

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const allColumns: EnhancedColumnType<LeadDataSource> = useMemo(
        () => [
            {
                key: ApplicationColumns.Company,
                dataIndex: ['module', 'company', 'displayName'],
                filterDropdown: props => showFilterDropDown(props, companyList),
                filteredValue: filter.companyIds,
                filterIcon: renderFilterIcon,
                title: t('leadListPage:columns.company'),
                sorter: true,
                sortOrder: companySort,
            },
            {
                key: ApplicationColumns.AppDate,
                dataIndex: ['versioning', 'createdAt'],
                render: (value, record: LeadListDataFragment) =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: getOr(company?.timeZone, 'module.company.timeZone', record),
                              withOffset: !forCi,
                          })
                        : '',
                title: t(`leadListPage:columns.date${leadStage}`),
                sorter: true,
                sortOrder: appDateSort,
            },
            {
                key: ApplicationColumns.LeadGenFormName,
                dataIndex: ['event', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, t('leadListPage:columns.leadGenFormName')),
                filteredValue: filter.eventDisplayName ? [filter.eventDisplayName] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.leadGenFormName'),
                sorter: true,
                sortOrder: leadGenFormNameSort,
                hidden:
                    dataSource.length > 0 &&
                    !dataSource.some(item => item.__typename === 'EventLead') &&
                    !filter.eventDisplayName,
            },
            {
                key: ApplicationColumns.LeadGenFormCampaignId,
                dataIndex: ['campaignValues', 'capCampaignId'],
                filterDropdown: props => showSearchDropDown(props, t('leadListPage:columns.leadGenFormCampaignId')),
                filteredValue: filter.eventCampaignId ? [filter.eventCampaignId] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.leadGenFormCampaignId'),
                sorter: true,
                sortOrder: leadGenFormCampaignIdSort,
                hidden:
                    dataSource.length > 0 &&
                    !dataSource.some(item => item.__typename === 'EventLead') &&
                    !filter.eventCampaignId,
            },
            {
                key: ApplicationColumns.Identifier,
                dataIndex: 'identifier',
                filterDropdown: props => showSearchDropDown(props, 'identifier'),
                filteredValue: filter.identifier ? [filter.identifier] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.id'),
                sorter: true,
                sortOrder: identifierSort,
            },
            {
                key: ApplicationColumns.Assignee,
                dataIndex: ['assignee', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, 'assignee'),
                filteredValue: filter.assignee ? [filter.assignee] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.assignedTo'),
                render: (value, record) => value,
                sorter: true,
                sortOrder: assigneeSort,
            },
            {
                key: ApplicationColumns.Applicant,
                dataIndex: ['customer', 'fullName'],
                filterDropdown: props => showSearchDropDown(props, 'applicant'),
                filteredValue: filter.customer ? [filter.customer] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.customer'),
                sorter: true,
                sortOrder: customerSort,
                hidden:
                    dataSource.length > 0 &&
                    !dataSource.some(
                        item =>
                            'customer' in item &&
                            item.customer.__typename !== 'Guarantor' &&
                            !isEmpty(item.customer.fullName)
                    ),
            },
            {
                key: ApplicationColumns.Vehicle,
                dataIndex: ['vehicle', 'name'],
                filterDropdown: props => showSearchDropDown(props, 'vehicle'),
                filteredValue: filter.vehicle ? [filter.vehicle] : undefined,
                filterIcon: renderSearchIcon,
                render: (value, record) => translatedString(record.vehicle?.name),
                title: t('leadListPage:columns.vehicle'),
                sorter: true,
                sortOrder: vehicleNameSort,
            },
            {
                key: ApplicationColumns.Module,
                dataIndex: ['module', 'displayName'],
                filterDropdown: props => showFilterDropDown(props, moduleList),
                filteredValue: filter.moduleIds,
                filterIcon: renderFilterIcon,
                render: (value, record) => getSourceValue(record, t),
                title: t('leadListPage:columns.source'),
                sorter: true,
                sortOrder: moduleSort,
            },
            {
                key: ApplicationColumns.BusinessPartnerId,
                dataIndex: ['capValues', 'businessPartnerId'],
                filterDropdown: props => showSearchDropDown(props, 'businessPartnerId'),
                filteredValue: filter.businessPartnerId ? [filter.businessPartnerId] : undefined,
                filterIcon: renderSearchIcon,
                title: t('leadListPage:columns.businessPartnerId'),
                sorter: true,
                sortOrder: businessPartnerIdSort,
            },
            {
                key: ApplicationColumns.Status,
                dataIndex: 'status',
                filterDropdown: props => showFilterDropDown(props, statusList),
                filteredValue: filter.status,
                filterIcon: renderFilterIcon,
                render: value => renderStatusTag(value, t),
                title: t('leadListPage:columns.status'),
                sorter: true,
                sortOrder: applicationStatusSort,
            },
        ],
        [
            filter,
            t,
            companySort,
            leadStage,
            appDateSort,
            leadGenFormNameSort,
            dataSource,
            leadGenFormCampaignIdSort,
            identifierSort,
            assigneeSort,
            customerSort,
            vehicleNameSort,
            moduleSort,
            businessPartnerIdSort,
            applicationStatusSort,
            showFilterDropDown,
            companyList,
            formatDate,
            company?.timeZone,
            forCi,
            showSearchDropDown,
            translatedString,
            moduleList,
            statusList,
        ]
    );

    const columns = useMemo(
        () =>
            allColumns.filter(
                ({ key, hidden = false }: { key: ApplicationColumns; hidden?: boolean }) =>
                    desiredColumns.includes(key) && !hidden
            ),
        [allColumns, desiredColumns]
    );

    const onListRow = useCallback<PaginatedLeadTable['onRow']>(
        record => ({
            onClick: () => {
                setCache(state);
                onRow ? onRow(record.isLead, record.versioning.suiteId) : navigate(record.versioning.suiteId);
            },
        }),
        [navigate, onRow, setCache, state]
    );

    const rowSelection = useMemo(() => {
        if (setSelectedLead) {
            return {
                onChange: selectedRowKeys => {
                    if (!selectedRowKeys) {
                        return;
                    }

                    setSelectedLead(selectedRowKeys);
                },
            };
        }

        return undefined;
    }, [setSelectedLead]);

    return (
        <PaginatedTableWithContext
            columns={columns}
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            onChange={onChange}
            onRow={onListRow}
            rowKey="id"
            rowSelection={isAllowSelection && rowSelection}
            scroll={{ x: true }}
            state={state}
            tableName={t(`leadListPage:title.${leadStage}`)}
            total={total}
        />
    );
};

export default LeadList;
