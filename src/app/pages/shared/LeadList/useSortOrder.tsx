import { useMemo } from 'react';
import { LeadSortingField } from '../../../api/types';

const useSortOrder = sort =>
    useMemo(() => {
        const companySort = sort.field === LeadSortingField.Company ? sort.orderValue : undefined;
        const appDateSort = sort.field === LeadSortingField.CreatedAt ? sort.orderValue : undefined;
        const identifierSort = sort.field === LeadSortingField.Identifier ? sort.orderValue : undefined;
        const assigneeSort = sort.field === LeadSortingField.Assignee ? sort.orderValue : undefined;
        const customerSort = sort.field === LeadSortingField.Customer ? sort.orderValue : undefined;
        const businessPartnerIdSort = sort.field === LeadSortingField.BusinessPartnerId ? sort.orderValue : undefined;
        const vehicleNameSort = sort.field === LeadSortingField.VehicleName ? sort.orderValue : undefined;
        const moduleSort = sort.field === LeadSortingField.Module ? sort.orderValue : undefined;
        const applicationStatusSort = sort.field === LeadSortingField.LeadStatus ? sort.orderValue : undefined;
        const leadGenFormNameSort = sort.field === LeadSortingField.LeadGenFormName ? sort.orderValue : undefined;
        const leadGenFormCampaignIdSort =
            sort.field === LeadSortingField.LeadGenFormCampaignId ? sort.orderValue : undefined;

        return {
            companySort,
            appDateSort,
            identifierSort,
            assigneeSort,
            customerSort,
            vehicleNameSort,
            moduleSort,
            applicationStatusSort,
            leadGenFormNameSort,
            leadGenFormCampaignIdSort,
            businessPartnerIdSort,
        };
    }, [sort]);

export default useSortOrder;
