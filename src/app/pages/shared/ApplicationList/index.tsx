import type { InputRef } from 'antd';
import type { ColumnFilterItem, ColumnsType, FilterDropdownProps } from 'antd/es/table/interface';
import { concat, getOr, isEmpty, isNil, pick, sortBy, uniq, intersection } from 'lodash/fp';
import type { Key } from 'react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { ApplicationListDataFragment } from '../../../api/fragments/ApplicationListData';
import { useGetApplicationsListFiltersQuery } from '../../../api/queries/getApplicationsListFilter';
import { useListApplicationsQuery } from '../../../api/queries/listApplications';
import type { ApplicationKind, LeadStageOption } from '../../../api/types';
import { ApplicationStatus, ApplicationStage, ModuleType } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import type { PaginatedTableProps } from '../../../components/PaginatedTable';
import RangeDateFilterBox, { objectToRangeDateValue } from '../../../components/RangeDateFilterBox';
import SearchBox from '../../../components/SearchBox';
import { useCompanies, useCompany } from '../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../themes/hooks';
import { getPathByStage, getTableName } from '../../../utilities/application';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useCompanyFormats from '../../../utilities/useCompanyFormats';
import useFormatDate from '../../../utilities/useFormatDate';
import useSortAndFilterCache from '../../../utilities/useSortAndFilterCache';
import useTranslatedString from '../../../utilities/useTranslatedString';
import { getTradeInVehicle, TradeInRequestColumns } from '../../admin/ApplicationTradeInListPage/helpers';
import {
    ApplicationColumns,
    getApplicationAppointmentDataIndex,
    getApplicationAppointmentDateLabel,
    getApplicationCacheKey,
    getApplicationDateLabel,
    getApplicationInsurancePremium,
    getApplicationVariant,
    getFinderVin,
    getSortField,
    renderBank,
    renderFinanceProduct,
    renderStatusTag,
} from './helpers';
import useListReducer from './useListReducer';
import useSortOrder from './useSortOrder';

export {
    ApplicationColumns,
    getApplicationDateLabel,
    getApplicationVariant,
    getSortField,
    renderBank,
    renderFinanceProduct,
    renderStatusTag,
};

type ApplicationDataSource = ApplicationListDataFragment & { key: Key };

type PaginatedApplicationTable = PaginatedTableProps<ApplicationDataSource>;

export type EnhancedColumnType<T = unknown> = ColumnsType<T> & { hidden?: boolean };

export type ApplicationListProps = {
    kind?: ApplicationKind;
    dealerIds?: string[];
    moduleIds?: string[];
    stage: ApplicationStage;
    showColumns: ApplicationColumns[];
    eventId?: string;
    configuratorId?: string;
    setSelectedApplication?: React.Dispatch<React.SetStateAction<string[]>>;
    setLoadedApplicationIds?: React.Dispatch<React.SetStateAction<string[]>>;
    onRow?: (suiteId: string) => void;
    setModuleListByApplication?: React.Dispatch<React.SetStateAction<string[]>>;
    finderVehicleVin?: string;
    leadStage?: LeadStageOption;
    isAllowSelection?: boolean;
    forCi?: boolean;
};

const ApplicationList = ({
    kind,
    dealerIds,
    moduleIds,
    stage,
    showColumns,
    eventId,
    configuratorId,
    setSelectedApplication,
    setLoadedApplicationIds,
    onRow,
    setModuleListByApplication,
    finderVehicleVin,
    leadStage,
    isAllowSelection = true,
    forCi = false,
}: ApplicationListProps) => {
    const { t } = useTranslation([
        'applicationList',
        'common',
        'reservationList',
        'mobilityList',
        'appointmentList',
        'insuranceList',
        'visitAppointmentList',
        'tradeInRequestList',
    ]);
    const { formatAmountWithCurrency } = useCompanyFormats();
    const [currentCache, setCache] = useSortAndFilterCache(getApplicationCacheKey(stage));

    const inputRef = useRef<InputRef>(null);
    const navigate = useNavigate();
    const [state, dispatch] = useListReducer(stage, currentCache);
    const { page, pageSize, sort, filter } = state;
    const translatedString = useTranslatedString();
    const { PaginatedTableWithContext } = useThemeComponents();

    const company = useCompany(true);
    const companies = useCompanies();

    const formatDate = useFormatDate();

    const { data } = useListApplicationsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: {
                kind,
                dealerIds,
                moduleIds,
                stage,
                eventId,
                configuratorId,
                finderVehicleVin,
                ...([ApplicationStage.VisitAppointment, ApplicationStage.Appointment].includes(stage) && {
                    appointmentModuleFilter:
                        stage === ApplicationStage.Appointment
                            ? ModuleType.AppointmentModule
                            : ModuleType.VisitAppointmentModule,
                }),
                leadStage,
                ...filter,
            },
        },
        onCompleted(response) {
            if (setLoadedApplicationIds) {
                setLoadedApplicationIds(currentReservationIds =>
                    uniq(
                        concat(
                            currentReservationIds,
                            concat(
                                currentReservationIds,
                                response.list.items.map(item => item.id)
                            )
                        )
                    )
                );
            }
        },
    });

    const { data: appFilterList } = useGetApplicationsListFiltersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyIds: company ? [company.id] : undefined,
                kind,
                dealerIds,
                moduleIds,
                stage,
                eventId,
                configuratorId,
            },
        },
    });

    const [statusList, moduleList, companyList] = useMemo(() => {
        const filterList = appFilterList?.getApplicationsListFilters;
        if (!filterList) {
            return [[], [], []];
        }

        const statusList = sortBy(
            'text',
            filterList.applicationStatus.map(status => ({
                text: t(`applicationList:status.${status}`),
                value: status,
            }))
        );

        const moduleList = sortBy(
            'text',
            filterList.modules.map(module => ({
                text: module.displayName,
                value: module.id,
            }))
        );

        const companyList = sortBy(
            'text',
            filterList.companies.map(company => ({
                text: company.displayName,
                value: company.id,
            }))
        );

        return [statusList, moduleList, companyList];
    }, [appFilterList?.getApplicationsListFilters, t]);

    const hasFinderVehicleModule = useMemo(
        () =>
            // Use `companies`; handles the case when the user selects all companies
            companies?.some(company => company.availableModules.hasFinderVehicleManagementModules),
        [companies]
    );

    useEffect(() => {
        if (setModuleListByApplication) {
            const moduleListIds = moduleList.map(module => module.value);
            setModuleListByApplication(moduleListIds);
        }
    }, [moduleList, setModuleListByApplication]);

    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const showSearchDropDown = useCallback(
        (props: FilterDropdownProps, dataIndex: string) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const showRangeFilterDropDown = useCallback(
        (props: FilterDropdownProps) =>
            RangeDateFilterBox({
                filterDropDownProps: props,
                picker: 'date',
            }),
        []
    );

    const processStatusFilter = useCallback(
        (filterStatuses: ApplicationStatus[]) => {
            if (!filterStatuses) {
                return undefined;
            }

            if (stage !== ApplicationStage.Appointment) {
                return filterStatuses;
            }

            // For Appointment stage: if either Completed or TestDriveCompleted is selected, include both
            const completionPair: ApplicationStatus[] = [
                ApplicationStatus.Completed,
                ApplicationStatus.TestDriveCompleted,
            ];
            const hasEitherCompletion = intersection(completionPair, filterStatuses).length > 0;

            return hasEitherCompletion ? uniq(concat(filterStatuses, completionPair)) : filterStatuses;
        },
        [stage]
    );

    const onChange = useCallback<PaginatedApplicationTable['onChange']>(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort': {
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });
                }

                case 'filter': {
                    return dispatch({
                        type: 'setFilter',
                        filteringRule: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            bank: !filters.bank ? undefined : (filters.bank[0] as string),
                            insurer: !filters.insuranceCompany ? undefined : (filters.insuranceCompany[0] as string),
                            financeProduct: !filters.financeProduct ? undefined : (filters.financeProduct[0] as string),
                            customer: !filters.applicant ? undefined : (filters.applicant[0] as string),
                            businessPartnerId: !filters.businessPartnerId
                                ? undefined
                                : (filters.businessPartnerId[0] as string),
                            vehicle: !filters.vehicle ? undefined : (filters.vehicle[0] as string),
                            identifier: !filters.identifier ? undefined : (filters.identifier[0] as string),
                            moduleIds: !filters.module ? undefined : (filters.module as string[]),
                            transactionId: !filters.transactionId ? undefined : (filters.transactionId[0] as string),
                            assignee: !filters.assignee ? undefined : (filters.assignee[0] as string),
                            status: !filters.status
                                ? undefined
                                : processStatusFilter(filters.status as ApplicationStatus[]),
                            appointmentDateRange: !filters.appointmentDate
                                ? undefined
                                : {
                                      start: new Date(filters.appointmentDate[0] as string),
                                      end: new Date(filters.appointmentDate[1] as string),
                                  },
                            finderVehicleVin: !filters.finderVin ? undefined : (filters.finderVin[0] as string),
                            eventDisplayName: !filters.leadGenFormName
                                ? undefined
                                : (filters.leadGenFormName[0] as string),
                            eventCampaignId: !filters.leadGenFormCampaignId
                                ? undefined
                                : (filters.leadGenFormCampaignId[0] as string),
                        },
                    });
                }

                default:
                    return undefined;
            }
        },
        [dispatch, stage]
    );

    const stagePath = useMemo(() => getPathByStage(stage), [stage]);

    const tableName = useMemo(() => getTableName(stage, t), [stage, t]);

    const {
        companySort,
        appDateSort,
        appointmentDateSort,
        identifierSort,
        assigneeSort,
        customerSort,
        vehicleNameSort,
        financeProductSort,
        bankSort,
        insurerSort,
        insurancePremiumSort,
        transactionIdSort,
        lastActivitySort,
        moduleSort,
        applicationStatusSort,
        finderVin,
        leadGenFormNameSort,
        leadGenFormCampaignIdSort,
        businessPartnerIdSort,
    } = useSortOrder(sort);

    const allColumns: EnhancedColumnType<ApplicationDataSource> = useMemo(
        () => [
            {
                key: ApplicationColumns.Company,
                dataIndex: ['module', 'company', 'displayName'],
                filterDropdown: props => showFilterDropDown(props, companyList),
                filteredValue: filter.companyIds,
                filterIcon: renderFilterIcon,
                title: t('applicationList:columns.company'),
                sorter: true,
                sortOrder: companySort,
            },
            {
                key: ApplicationColumns.AppDate,
                dataIndex: ['versioning', 'createdAt'],
                render: (value, record: ApplicationListDataFragment) =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: getOr(company?.timeZone, 'module.company.timeZone', record),
                              withOffset: !(forCi && stage === ApplicationStage.Appointment),
                          })
                        : '',
                title: t(`applicationList:columns.${getApplicationDateLabel(stage)}`),
                sorter: true,
                sortOrder: appDateSort,
            },
            {
                key: ApplicationColumns.LeadGenFormName,
                dataIndex: ['event', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, t('applicationList:columns.leadGenFormName')),
                filteredValue: filter.eventDisplayName ? [filter.eventDisplayName] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.leadGenFormName'),
                sorter: true,
                sortOrder: leadGenFormNameSort,
                hidden: !dataSource.some(item => item.__typename === 'EventApplication') && !filter.eventDisplayName,
            },
            {
                key: ApplicationColumns.LeadGenFormCampaignId,
                dataIndex: ['campaignValues', 'capCampaignId'],
                filterDropdown: props => showSearchDropDown(props, t('applicationList:columns.leadGenFormCampaignId')),
                filteredValue: filter.eventCampaignId ? [filter.eventCampaignId] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.leadGenFormCampaignId'),
                sorter: true,
                sortOrder: leadGenFormCampaignIdSort,
                hidden: !dataSource.some(item => item.__typename === 'EventApplication') && !filter.eventCampaignId,
            },
            {
                key: ApplicationColumns.AppointmentDate,
                dataIndex: getApplicationAppointmentDataIndex(stage),
                filterDropdown: props => showRangeFilterDropDown(props),
                filteredValue: objectToRangeDateValue(filter.appointmentDateRange),
                render: (value, record: ApplicationListDataFragment) =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: getOr(company?.timeZone, 'module.company.timeZone', record),
                              withOffset: true,
                          })
                        : '',
                title: t(`applicationList:columns.${getApplicationAppointmentDateLabel(stage)}`),
                sorter: true,
                sortOrder: appointmentDateSort,
            },
            {
                key: ApplicationColumns.Identifier,
                dataIndex: [stagePath, 'identifier'],
                filterDropdown: props => showSearchDropDown(props, 'identifier'),
                filteredValue: filter.identifier ? [filter.identifier] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.id'),
                sorter: true,
                sortOrder: identifierSort,
            },
            {
                key: ApplicationColumns.Assignee,
                dataIndex: [stagePath, 'assignee', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, 'assignee'),
                filteredValue: filter.assignee ? [filter.assignee] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.assignedTo'),
                render: (value, record) => value,
                sorter: true,
                sortOrder: assigneeSort,
            },
            {
                key: ApplicationColumns.Applicant,
                dataIndex: ['applicant', 'fullName'],
                filterDropdown: props => showSearchDropDown(props, 'applicant'),
                filteredValue: filter.customer ? [filter.customer] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.customer'),
                sorter: true,
                sortOrder: customerSort,
                hidden:
                    dataSource.length > 0 &&
                    !dataSource.some(
                        item =>
                            'applicant' in item &&
                            item.applicant.__typename !== 'Guarantor' &&
                            !isEmpty(item.applicant.fullName)
                    ),
            },
            {
                key: ApplicationColumns.Vehicle,
                dataIndex: ['vehicle', 'name'],
                filterDropdown: props => showSearchDropDown(props, 'vehicle'),
                filteredValue: filter.vehicle ? [filter.vehicle] : undefined,
                filterIcon: renderSearchIcon,
                render: (value, record) => getApplicationVariant(value, record, translatedString),
                title: t('applicationList:columns.vehicle'),
                sorter: true,
                sortOrder: vehicleNameSort,
            },
            {
                key: ApplicationColumns.FinderVin,
                dataIndex: ['vehicle', 'listing', 'vehicle', 'vin'],
                filterDropdown: props => showSearchDropDown(props, 'finderVin'),
                filteredValue: filter.vehicle ? [filter.vehicle] : undefined,
                filterIcon: renderSearchIcon,
                render: (value, record) => getFinderVin(value, record),
                title: t('applicationList:columns.finderVin'),
                sorter: true,
                sortOrder: finderVin,
                hidden: !hasFinderVehicleModule,
            },
            {
                key: ApplicationColumns.FinanceProduct,
                dataIndex: ['financeProduct', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, 'financeProduct'),
                filteredValue: filter.financeProduct ? [filter.financeProduct] : undefined,
                filterIcon: renderSearchIcon,
                render: renderFinanceProduct,
                title: t('applicationList:columns.financialProductName'),
                sorter: true,
                sortOrder: financeProductSort,
            },
            {
                key: ApplicationColumns.Bank,
                dataIndex: ['bank', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, 'bank'),
                filteredValue: filter.bank ? [filter.bank] : undefined,
                filterIcon: renderSearchIcon,
                render: renderBank,
                title: t('applicationList:columns.bankName'),
                sorter: true,
                sortOrder: bankSort,
            },
            {
                key: ApplicationColumns.InsuranceCompany,
                dataIndex: ['insurer', 'displayName'],
                filterDropdown: props => showSearchDropDown(props, 'insurer'),
                filteredValue: filter.insurer ? [filter.insurer] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.insuranceCompany'),
                sorter: true,
                sortOrder: insurerSort,
            },
            {
                key: ApplicationColumns.InsurancePremium,
                dataIndex: ['insurancing', 'insurancePremium'],
                render: (value, record) =>
                    getApplicationInsurancePremium(
                        value,
                        record,
                        formatAmountWithCurrency,
                        t('applicationList:values.noInsurancePremium')
                    ),
                title: t('applicationList:columns.insurancePremium', {
                    currency: company ? `(${company.currency})` : '',
                }),
                sorter: true,
                sortOrder: insurancePremiumSort,
            },
            {
                key: ApplicationColumns.TransactionId,
                dataIndex: ['deposit', 'transactionId'],
                filterDropdown: props => showSearchDropDown(props, 'deposit'),
                filteredValue: filter.transactionId ? [filter.transactionId] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.transactionId'),
                sorter: true,
                sortOrder: transactionIdSort,
            },
            {
                key: ApplicationColumns.LastActivity,
                dataIndex: ['versioning', 'updatedAt'],
                render: (value, record) =>
                    formatDate({
                        date: value,
                        timeZone: getOr(company?.timeZone, 'module.company.timeZone', record),
                        withOffset: true,
                    }),
                title: t('applicationList:columns.lastUpdated'),
                sorter: true,
                sortOrder: lastActivitySort,
            },
            {
                key: ApplicationColumns.Module,
                dataIndex: ['module', 'displayName'],
                filterDropdown: props => showFilterDropDown(props, moduleList),
                filteredValue: filter.moduleIds,
                filterIcon: renderFilterIcon,
                title: t('applicationList:columns.module'),
                sorter: true,
                sortOrder: moduleSort,
            },

            {
                key: ApplicationColumns.TradeInSalesConsultant,
                dataIndex: ['tradeInStage', 'assignee', 'displayName'],
                title: t('tradeInRequestList:columns.salesConsultant'),
            },
            {
                key: ApplicationColumns.TradeInCustomer,
                dataIndex: ['tradeInStage', 'assignee', 'displayName'],
                title: t('tradeInRequestList:columns.customer'),
            },
            {
                key: ApplicationColumns.VehicleMake,
                dataIndex: ['tradeInVehicle'],
                title: t('tradeInRequestList:columns.vehicleMake'),
                render: (value, record) => getTradeInVehicle(value, record, TradeInRequestColumns.VehicleMake),
            },
            {
                key: ApplicationColumns.VehicleModel,
                dataIndex: ['tradeInVehicle'],
                title: t('tradeInRequestList:columns.vehicleModel'),
                render: (value, record) => getTradeInVehicle(value, record, TradeInRequestColumns.VehicleModel),
            },
            {
                key: ApplicationColumns.BusinessPartnerId,
                dataIndex: ['lead', 'capValues', 'businessPartnerId'],
                filterDropdown: props => showSearchDropDown(props, 'businessPartnerId'),
                filteredValue: filter.businessPartnerId ? [filter.businessPartnerId] : undefined,
                filterIcon: renderSearchIcon,
                title: t('applicationList:columns.businessPartnerId'),
                sorter: true,
                sortOrder: businessPartnerIdSort,
            },
            {
                key: ApplicationColumns.Status,
                dataIndex: [stagePath, 'status'],
                filterDropdown: props => showFilterDropDown(props, statusList),
                filteredValue: filter.status,
                filterIcon: renderFilterIcon,
                render: value => renderStatusTag(value, stage, t),
                title: t('applicationList:columns.status'),
                sorter: true,
                sortOrder: applicationStatusSort,
            },
        ],
        [
            filter,
            t,
            companySort,
            stage,
            appDateSort,
            leadGenFormNameSort,
            dataSource,
            leadGenFormCampaignIdSort,
            appointmentDateSort,
            stagePath,
            identifierSort,
            assigneeSort,
            customerSort,
            vehicleNameSort,
            finderVin,
            hasFinderVehicleModule,
            financeProductSort,
            bankSort,
            insurerSort,
            company,
            insurancePremiumSort,
            transactionIdSort,
            lastActivitySort,
            moduleSort,
            businessPartnerIdSort,
            applicationStatusSort,
            showFilterDropDown,
            companyList,
            formatDate,
            forCi,
            showSearchDropDown,
            showRangeFilterDropDown,
            translatedString,
            formatAmountWithCurrency,
            moduleList,
            statusList,
        ]
    );

    const onListRow = useCallback<PaginatedApplicationTable['onRow']>(
        record => ({
            onClick: () => {
                setCache(state);
                if (record.__typename === 'LaunchpadApplication' && stage === ApplicationStage.TradeIn) {
                    window.location.href = record.detailUrl;
                    // navigate(record.detailUrl);
                } else {
                    onRow ? onRow(record.versioning.suiteId) : navigate(record.versioning.suiteId);
                }
            },
        }),
        [navigate, onRow, setCache, stage, state]
    );

    const columns = useMemo(
        () =>
            allColumns.filter(
                ({ key, hidden = false }: { key: ApplicationColumns; hidden?: boolean }) =>
                    showColumns.includes(key) && !hidden
            ),
        [allColumns, showColumns]
    );

    const rowSelection = useMemo(() => {
        if (setSelectedApplication && isAllowSelection) {
            return {
                onChange: selectedRowKeys => {
                    if (!selectedRowKeys) {
                        return;
                    }

                    setSelectedApplication(selectedRowKeys);
                },
            };
        }

        return undefined;
    }, [setSelectedApplication, isAllowSelection]);

    return (
        <PaginatedTableWithContext
            columns={columns}
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            onChange={onChange}
            onRow={onListRow}
            rowKey="id"
            rowSelection={rowSelection}
            scroll={{ x: true }}
            state={state}
            tableName={tableName}
            total={total}
        />
    );
};

export default ApplicationList;
