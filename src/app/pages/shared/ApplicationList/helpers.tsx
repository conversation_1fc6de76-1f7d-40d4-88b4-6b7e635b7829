import { Tag } from 'antd';
import type { TFunction } from 'i18next';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import type { ApplicationListDataFragment } from '../../../api/fragments/ApplicationListData';
import type { TranslatedStringDataFragment } from '../../../api/fragments/TranslatedStringData';
import {
    ApplicationSortingField,
    ApplicationStage,
    ApplicationStatus,
    LeadStatus,
    ModuleType,
} from '../../../api/types';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import type { NumberFormatFn } from '../../../utilities/useFormats';
import { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';

export enum ApplicationColumns {
    AppDate = 'appDate',
    Identifier = 'identifier',
    Vehicle = 'vehicle',
    Assignee = 'assignee',
    Applicant = 'applicant',
    BusinessPartnerId = 'businessPartnerId',
    FinanceProduct = 'financeProduct',
    Bank = 'bank',
    Status = 'status',
    TransactionId = 'transactionId',
    LastActivity = 'lastActivity',
    Module = 'module',
    AppointmentDate = 'appointmentDate',
    InsuranceCompany = 'insuranceCompany',
    InsurancePremium = 'insurancePremium',
    Company = 'company',
    FinderVin = 'finderVin',
    CapStatus = 'capStatus',
    LeadGenFormName = 'leadGenFormName',
    LeadGenFormCampaignId = 'leadGenFormCampaignId',
    TradeInSalesConsultant = 'tradeInSalesConsultant',
    TradeInCustomer = 'tradeInCustomer',
    VehicleMake = 'vehicleMake',
    VehicleModel = 'vehicleModel',
    VehicleVariant = 'vehicleVariant',
}

export const getStatusColor = (status: string): string => {
    switch (status) {
        case ApplicationStatus.ApplicantDetailsReceived:
        case ApplicationStatus.ApplicationReceived:
        case ApplicationStatus.SigningCompleted:
        case ApplicationStatus.OtpCompleted:
        case ApplicationStatus.PaymentCompleted:
        case ApplicationStatus.PaymentReceivedOffline:
        case ApplicationStatus.NewAppointment:
        case ApplicationStatus.NewLead:
        case ApplicationStatus.NewMobility:
        case ApplicationStatus.GuarantorSigningCompleted:
        case ApplicationStatus.GuarantorDetailsReceived:
        case ApplicationStatus.Shared:
        case ApplicationStatus.Contacted:
        case ApplicationStatus.PaymentReceived:
        case ApplicationStatus.FinancingRequest:
        case ApplicationStatus.TestDriveSigningCompleted:
        case ApplicationStatus.BookingConfirmed:
            return 'blue';

        case ApplicationStatus.ApplicantDetailsPendingUpdate:
        case ApplicationStatus.PendingCustomerConfirmation:
        case ApplicationStatus.SubmittedToSystem:
        case ApplicationStatus.Lead:
        case ApplicationStatus.SigningInitiated:
        case ApplicationStatus.OtpPending:
        case ApplicationStatus.SubmittedToBank:
        case ApplicationStatus.SubmittedToInsuranceCompany:
        case ApplicationStatus.PaymentPending:
        case ApplicationStatus.ResubmittedToBank:
        case ApplicationStatus.ResubmittedToSystem:
        case ApplicationStatus.GuarantorSigningInitiated:
        case ApplicationStatus.GuarantorDetailsPendingUpdate:
        case ApplicationStatus.GuarantorSigningPending:
        case ApplicationStatus.PendingDisbursement:
        case ApplicationStatus.SigningPending:
        case ApplicationStatus.TestDriveSigningPending:
        case ApplicationStatus.TestDriveSigningInitiated:
            return 'cyan';

        case ApplicationStatus.UnableToSubmit:
        case ApplicationStatus.CancellationdFailed:
        case ApplicationStatus.UnableToCancel:
        case ApplicationStatus.UnableToConnect:
        case ApplicationStatus.ConnectionFailed:
        case ApplicationStatus.SigningCreationFailed:
        case ApplicationStatus.SigningTimeout:
        case ApplicationStatus.SigningRejected:
        case ApplicationStatus.UnabletoMakePayment:
        case ApplicationStatus.PaymentFailed:
        case ApplicationStatus.PaymentTimeout:
        case ApplicationStatus.SubmissionFailed:
        case ApplicationStatus.SubmissionToBankFailed:
        case ApplicationStatus.SubmissionToInsuranceCompanyFailed:
        case ApplicationStatus.ResubmissionToBankFailed:
        case ApplicationStatus.GuarantorSigningRejected:
        case ApplicationStatus.TestDriveSigningRejected:
            return 'red';

        case ApplicationStatus.InsuranceApproved:
        case ApplicationStatus.Approved:
        case ApplicationStatus.CheckIn:
        case ApplicationStatus.AppointmentCheckIn:
        case ApplicationStatus.TestDriveStarted:
            return 'green';

        case ApplicationStatus.InsuranceDeclined:
        case ApplicationStatus.Declined:
            return 'magenta';

        case ApplicationStatus.Completed:
        case ApplicationStatus.Activated:
        case ApplicationStatus.TestDriveCompleted:
            return 'purple';

        case ApplicationStatus.InsuranceCancelled:
        case ApplicationStatus.Cancelled:
        case ApplicationStatus.PaymentRefunded:
            return 'gold';

        case ApplicationStatus.Drafted:
            return 'grey';
        case ApplicationStatus.TradeInQuoted:
            return 'green';
        case ApplicationStatus.TradeInPending:
            return 'gold';
        default:
            return '';
    }
};

const getLeadStatusColor = (status: LeadStatus) => {
    switch (status) {
        case LeadStatus.PendingQualify:
            return { background: '#E6FFFB ', font: '#08979C ', border: '#08979C ' };

        case LeadStatus.SubmissionFailed:
            return { background: '#FFF1F0 ', font: '#CF1322 ', border: '#CF1322 ' };

        case LeadStatus.SubmittedToCap:
            return { background: '#E6FFFB ', font: '#08979C', border: '#08979C' };

        case LeadStatus.SubmittedWithError:
            return { background: '#FFF1F0', font: '#CF1322', border: '#CF1322' };

        case LeadStatus.Unqualified:
            return { background: '#FAFAFA ', font: '#000000 ', border: '262626' };

        default:
            return { background: '#0000000a', font: '#000000', border: '#0505050f' };
    }
};

const getTradeInStatusColor = (status: string) => {
    switch (status) {
        case ApplicationStatus.TradeInQuoted:
            return { background: '#E4FFEC', font: '#010205', border: '#E4FFEC' };

        case ApplicationStatus.TradeInPending:
        default:
            return { background: '#FFF4D4', font: '#010205', border: '#FFF4D4' };
    }
};

const getFollowUpStatusColor = (status: string) => {
    switch (status) {
        case ApplicationStatus.FollowUpPlanned:
            return { background: '#E4FFEC', font: '#389e0d', border: '#b7eb8f' };

        case ApplicationStatus.FollowUpConfirmed:
            return { background: '#E6F7FF', font: '#096dd9', border: '#91d5ff' };

        default:
            return { background: '#FFF4D2', font: '#d48806', border: '#ffe58f' };
    }
};

export const renderLeadStatusTag = (value: LeadStatus, t: TFunction) => {
    const { background, font, border } = getLeadStatusColor(value);

    if (!value) {
        return null;
    }

    return (
        <Tag key={value} style={{ backgroundColor: background, color: font, borderColor: border }}>
            {t(`applicationList:leadStatus.${value}`)}
        </Tag>
    );
};

export const renderStatusTag = (value: string, stage: ApplicationStage, t: TFunction) => {
    if (stage === ApplicationStage.TradeIn) {
        const { background, font, border } = getTradeInStatusColor(value);

        return (
            <Tag key={value} style={{ backgroundColor: background, color: font, borderColor: border }}>
                {t(`applicationList:status.${value}`)}
            </Tag>
        );
    }

    if (stage === ApplicationStage.FollowUp) {
        const { background, font, border } = getFollowUpStatusColor(value);

        return (
            <Tag key={value} style={{ backgroundColor: background, color: font, borderColor: border }}>
                {t(`applicationList:status.${value}`)}
            </Tag>
        );
    }

    const color = getStatusColor(value);

    return (
        <Tag key={value} color={color}>
            {stage === ApplicationStage.Appointment && value === 'CheckIn'
                ? t(`applicationList:status.${stage}${value}`)
                : t(`applicationList:status.${value}`)}
        </Tag>
    );
};

export const renderFinanceProduct = (value, record: ApplicationListDataFragment) => {
    switch (record.__typename) {
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'StandardApplication':
        case 'FinderApplication':
            return record.financeProduct?.displayName;

        default:
            return null;
    }
};

export const renderBank = (value, record: ApplicationListDataFragment) => {
    switch (record.__typename) {
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'StandardApplication':
        case 'FinderApplication':
            return record.bank?.displayName;

        default:
            return null;
    }
};

export const getSortField = makeGetSortingRule((field): ApplicationSortingField => {
    switch (field) {
        case 'module.company.displayName':
            return ApplicationSortingField.Company;

        case 'bank.displayName':
            return ApplicationSortingField.Bank;

        case 'financingStage.identifier':
        case 'insuranceStage.identifier':
        case 'reservationStage.identifier':
        case 'appointmentStage.identifier':
        case 'visitAppointmentStage.identifier':
        case 'mobilityStage.identifier':
        case 'followUpStage.identifier':
            return ApplicationSortingField.Identifier;

        case 'financeProduct.displayName':
            return ApplicationSortingField.FinanceProduct;

        case 'vehicle.name':
            return ApplicationSortingField.VehicleName;

        case 'financingStage.status':
        case 'insuranceStage.status':
        case 'reservationStage.status':
        case 'appointmentStage.status':
        case 'visitAppointmentStage.status':
        case 'mobilityStage.status':
        case 'tradeInStage.status':
        case 'followUpStage.status':
            return ApplicationSortingField.ApplicationStatus;

        case 'versioning.createdAt':
            return ApplicationSortingField.ApplicationDate;

        case 'financingStage.assignee.displayName':
        case 'insuranceStage.assignee.displayName':
        case 'reservationStage.assignee.displayName':
        case 'leadStage.assignee.displayName':
        case 'appointmentStage.assignee.displayName':
        case 'visitAppointmentStage.assignee.displayName':
        case 'followUpStage.assignee.displayName':
            return ApplicationSortingField.Assignee;

        case 'deposit.transactionId':
            return ApplicationSortingField.TransactionId;

        case 'versioning.updatedAt':
            return ApplicationSortingField.LastActivity;

        case 'module.displayName':
            return ApplicationSortingField.Module;

        case 'applicant.fullName':
            return ApplicationSortingField.Customer;

        case 'appointmentStage.bookingTimeSlot.slot':
        case 'visitAppointmentStage.bookingTimeSlot.slot':
        case 'followUpStage.scheduledDate':
            return ApplicationSortingField.AppointmentDate;

        case 'insurer.displayName':
            return ApplicationSortingField.Insurer;

        case 'insurancing.insurancePremium':
            return ApplicationSortingField.InsurancePremium;

        case 'vehicle.listing.vehicle.vin':
            return ApplicationSortingField.FinderVin;

        case 'capValues.status':
            return ApplicationSortingField.CapStatus;

        case 'event.campaignId':
            return ApplicationSortingField.LeadGenFormCampaignId;

        case 'event.displayName':
            return ApplicationSortingField.LeadGenFormName;

        case 'lead.capValues.businessPartnerId':
            return ApplicationSortingField.BusinessPartnerId;

        default:
            throw new Error(`Such Field is not supported: ${field}`);
    }
});

export const getFinderVin = (value, record: ApplicationListDataFragment) => {
    switch (record.__typename) {
        case 'FinderApplication':
            return record.vehicle.__typename === 'FinderVehicle' && record.vehicle.listing.vehicle.vin;

        default:
            return '';
    }
};

export const getApplicationVariant = (
    value,
    record: ApplicationListDataFragment,
    translatedString: (value: TranslatedStringDataFragment) => string
) => {
    switch (record.__typename) {
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'StandardApplication':
        case 'FinderApplication':
        case 'LaunchpadApplication':
            return translatedString(record.vehicle?.name);

        default:
            return '';
    }
};

export const getApplicationInsurancePremium = (
    value: number | null | undefined,
    record: ApplicationListDataFragment,
    format: NumberFormatFn,
    fallback: string
) => {
    if (!isNil(value)) {
        return format(value);
    }

    return fallback;
};

export const getApplicationDateLabel = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing:
            return 'applicationDate';

        case ApplicationStage.Insurance:
            return 'insuranceDate';

        case ApplicationStage.Reservation:
            return 'reservationDate';

        case ApplicationStage.Lead:
            return 'leadDate';

        case ApplicationStage.Appointment:
        case ApplicationStage.VisitAppointment:
            return 'bookingDate';

        case ApplicationStage.TradeIn:
            return 'tradeInDate';

        case ApplicationStage.FollowUp:
            return 'creationDate';

        default:
            throw new Error('Application stage is not valid');
    }
};

export const getApplicationAppointmentDateLabel = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.FollowUp:
            return 'followUpDate';

        default:
            return 'appointmentDate';
    }
};

export const getApplicationAppointmentDataIndex = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.FollowUp:
            return ['followUpStage', 'scheduledDate'];

        case ApplicationStage.Appointment:
            return ['appointmentStage', 'bookingTimeSlot', 'slot'];

        case ApplicationStage.VisitAppointment:
            return ['visitAppointmentStage', 'bookingTimeSlot', 'slot'];

        default:
            return [];
    }
};

export const getApplicationCacheKey = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing:
            return SortAndFilterCacheKey.Financing;

        case ApplicationStage.Insurance:
            return SortAndFilterCacheKey.Insurance;

        case ApplicationStage.Reservation:
            return SortAndFilterCacheKey.Reservation;

        case ApplicationStage.Lead:
            return SortAndFilterCacheKey.Lead;

        case ApplicationStage.Appointment:
            return SortAndFilterCacheKey.Appointment;

        case ApplicationStage.VisitAppointment:
            return SortAndFilterCacheKey.VisitAppointment;

        case ApplicationStage.TradeIn:
            return SortAndFilterCacheKey.TradeIn;

        case ApplicationStage.FollowUp:
            return SortAndFilterCacheKey.FollowUp;

        default:
            throw new Error('Application stage is not valid');
    }
};

export const useAdditionalColumns = () => {
    const company = useCompany(true);

    return useMemo(
        () => ({
            hasEventModule: company?.availableModules?.hasEventModules,
            hasCapModule: company?.availableModules?.hasCapModules,
        }),
        [company]
    );
};

export const useApplicationColumns = (stage: ApplicationStage, options?: { hasModule?: boolean }) => {
    const { hasModule = true } = options || {};
    const { hasCapModule } = useAdditionalColumns();

    return useMemo(() => {
        switch (stage) {
            case ApplicationStage.Financing:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.FinanceProduct,
                    ApplicationColumns.Bank,
                    hasModule && ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.Reservation:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.TransactionId,
                    ApplicationColumns.LastActivity,
                    hasModule && ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.Insurance:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.InsuranceCompany,
                    ApplicationColumns.InsurancePremium,
                    hasModule && ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                    ApplicationColumns.LastActivity,
                ];

            case ApplicationStage.Appointment:
            case ApplicationStage.VisitAppointment:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.AppointmentDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Module,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.FinderVin,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.TradeIn:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.TradeInSalesConsultant,
                    ApplicationColumns.TradeInCustomer,
                    ApplicationColumns.VehicleMake,
                    ApplicationColumns.VehicleModel,
                    ApplicationColumns.VehicleVariant,
                    ApplicationColumns.Status,
                ];

            default:
                return [];
        }
    }, [hasCapModule, hasModule, stage]);
};

export const useApplicationColumnsForCI = (stage: ApplicationStage) => {
    const { hasEventModule, hasCapModule } = useAdditionalColumns();

    return useMemo(() => {
        switch (stage) {
            case ApplicationStage.Financing:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.FinanceProduct,
                    ApplicationColumns.Bank,
                    ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.Reservation:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.TransactionId,
                    ApplicationColumns.LastActivity,
                    ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.Insurance:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.InsuranceCompany,
                    ApplicationColumns.InsurancePremium,
                    ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                    ApplicationColumns.LastActivity,
                ];

            case ApplicationStage.Lead:
                return [
                    ApplicationColumns.AppDate,
                    hasEventModule && ApplicationColumns.LeadGenFormName,
                    hasEventModule && ApplicationColumns.LeadGenFormCampaignId,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.FinanceProduct,
                    ApplicationColumns.Bank,
                    ApplicationColumns.Module,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.Appointment:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.BusinessPartnerId,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.VisitAppointment:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.AppointmentDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Module,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    hasCapModule && ApplicationColumns.CapStatus,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.TradeIn:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.TradeInSalesConsultant,
                    ApplicationColumns.TradeInCustomer,
                    ApplicationColumns.VehicleMake,
                    ApplicationColumns.VehicleModel,
                    ApplicationColumns.VehicleVariant,
                    ApplicationColumns.Status,
                ];

            case ApplicationStage.FollowUp:
                return [
                    ApplicationColumns.AppDate,
                    ApplicationColumns.AppointmentDate,
                    ApplicationColumns.Identifier,
                    ApplicationColumns.Assignee,
                    ApplicationColumns.Applicant,
                    ApplicationColumns.Vehicle,
                    ApplicationColumns.Module,
                    ApplicationColumns.Status,
                ];

            default:
                return [];
        }
    }, [hasCapModule, hasEventModule, stage]);
};
