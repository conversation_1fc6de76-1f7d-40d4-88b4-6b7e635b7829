import { PButtonPure, PHeading } from '@porsche-design-system/components-react';
import { Col, Grid, Row } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicantIntegrationSectionContainer, ButtonContainer } from './ui';
import useApplicationIntegrationOnMobile from './useApplicationIntegrationOnMobile';

export type ApplicantKYCIntegrationSectionRef = {
    closeMobileIntegrationModal: () => void;
};

type ApplicantKYCIntegrationSectionProps = {
    actionButtons: JSX.Element[];
};

const ApplicantKYCIntegrationSection = forwardRef<
    ApplicantKYCIntegrationSectionRef,
    ApplicantKYCIntegrationSectionProps
>(({ actionButtons }, ref) => {
    const { t } = useTranslation([
        'journey',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'capApplication',
        'launchpadLeadDetails',
        'launchpadLeadList',
        'applicationDetails',
    ]);

    const screens = Grid.useBreakpoint();
    const applicationIntegrationOnMobile = useApplicationIntegrationOnMobile();

    useImperativeHandle(
        ref,
        () => ({
            closeMobileIntegrationModal: () => {
                applicationIntegrationOnMobile.close();
            },
        }),
        [applicationIntegrationOnMobile]
    );

    if (!actionButtons.length) {
        return null;
    }

    return (
        <>
            {screens.md ? (
                <ApplicantIntegrationSectionContainer>
                    <Col span={24}>
                        <Row>
                            <PHeading size="small">{t('journey:applicantIntegrationSection.title')}</PHeading>
                        </Row>
                        <ButtonContainer buttonCount={actionButtons.length}>
                            {actionButtons.map(button => button)}
                        </ButtonContainer>
                    </Col>
                </ApplicantIntegrationSectionContainer>
            ) : (
                <Col span={24}>
                    <PButtonPure icon="arrow-right" onClick={applicationIntegrationOnMobile.open} type="button">
                        {t('journey:applicantIntegrationSection.mobileCta')}
                    </PButtonPure>
                </Col>
            )}
            {applicationIntegrationOnMobile.render({ actionButtons })}
        </>
    );
});

export default ApplicantKYCIntegrationSection;
