import styled, { css } from 'styled-components';
import breakpoints from '../../../../utilities/breakpoints';

export const ApplicantIntegrationSectionContainer = styled.div`
    width: 100%;
    background-color: #eeeff2;
    padding: 16px;
    border-radius: 8px;

    @media (min-width: ${breakpoints.sm}) {
        padding: 24px;
    }
`;

const defaultMyinfoScale = css`
    .myinfo-singpass-btn {
        width: 318px;
    }
`;

const fillSpanButton = css`
    justify-content: flex-start;
    & div:has(> p-button) {
        min-width: 0;
        max-width: 100%;
    }
`;

// All button have the same width as base myinfo button
const uniformButtonSpan = css`
    & > * {
        flex: 0 0 318px;
        width: 318px;
    }

    ${defaultMyinfoScale}
    ${fillSpanButton}
`;

// All button adjusted so it can fill just half of container so it can have 2 column
const halfButtonSpan = css`
    & > * {
        flex: 0 0 290px;
        width: 290px;
    }

    .myinfo-singpass-btn {
        width: 290px;
    }

    ${fillSpanButton}
`;

// Default button span. Let the width of the button is auto following the content within the button
const defaultButtonSpan = css`
    & > * {
        flex: none;
        width: auto;
    }
    ${defaultMyinfoScale}
    ${fillSpanButton}
`;

export const ButtonContainer = styled.div<{ buttonCount: number; isMobile?: boolean }>`
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 8px;
    margin-top: ${({ isMobile }) => (isMobile ? '24px' : '16px')};
    justify-content: center;
    align-content: flex-start;

    // Mobile view start
    & > * {
        display: block;
        flex: 1 1 100%;
        max-width: 100%;
    }

    & div:has(> p-button) {
        min-width: 100%;

        & > p-button {
            width: 100%;
        }
    }

    .myinfo-singpass-btn {
         width: 100%;
    }

    @media (min-width: ${breakpoints.sm}) {
        ${({ buttonCount }) => (buttonCount > 1 ? halfButtonSpan : defaultButtonSpan)}

    @media (min-width: ${breakpoints.md}) {
        ${({ buttonCount }) => (buttonCount > 2 ? halfButtonSpan : defaultButtonSpan)}
    }

    @media (min-width: ${breakpoints.lg}) {
        ${({ buttonCount }) => (buttonCount > 3 ? uniformButtonSpan : defaultButtonSpan)}
    }

    @media (min-width: ${breakpoints.xl}) {
        ${({ buttonCount }) => (buttonCount > 4 ? uniformButtonSpan : defaultButtonSpan)}
    }

    @media (min-width: ${breakpoints.xxl}) {
        ${defaultButtonSpan}
    }
`;
