import { isEmpty } from 'lodash/fp';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { LayoutType } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import type { JourneyStage } from '../../portal/StandardApplicationEntrypoint/Journey/shared';
import { useJourneyStepsContext } from './JourneyStepsContext';
import { getCurrentStageIndex } from './mapJourneySteps';

export type JourneyStepsProps = {
    stages: JourneyStage[];
    currentStage: JourneyStage;
};

const StepWrapper = styled.div`
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3
            ? css`
                  padding-bottom: 36px;
              `
            : css`
                  padding-top: 16px;
                  padding-bottom: 50px;
              `}
`;

const JourneySteps = ({ stages, currentStage }: JourneyStepsProps) => {
    const { Steps } = useThemeComponents();
    const { t } = useTranslation('journey');
    const { onBack, shouldAllowBack } = useJourneyStepsContext();

    const current = useMemo(() => getCurrentStageIndex(stages || [], currentStage), [stages, currentStage]);

    const stepOnClick = useCallback(
        (index: number) => {
            if (index > current || current - index > 1 || !shouldAllowBack) {
                return;
            }

            if (onBack) {
                onBack();
            }
        },
        [current, onBack, shouldAllowBack]
    );

    if (isEmpty(stages)) {
        return null;
    }

    return (
        <StepWrapper>
            <Steps
                current={current}
                items={stages.map((stage, index) => ({
                    index,
                    title: t(`journey:steps.${stage}`),
                }))}
                stepRedirectOnClick={stepOnClick}
            />
        </StepWrapper>
    );
};

export default JourneySteps;
