/* eslint-disable max-len */
import { DownloadOutlined } from '@ant-design/icons';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { ApplicationListEndpointContextDataFragment } from '../../../../api/fragments/ApplicationListEndpointContextData';
import { useListModulesForApplicationDownloadQuery } from '../../../../api/queries/listModulesForApplicationDownload';
import { ApplicationStage, ModuleRole } from '../../../../api/types';
import LoadingElement from '../../../../components/LoadingElement';
import { useAccountContext } from '../../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import useDownloadOptions from '../../../../components/fields/DownloadModal/useDownloadOptions';
import BasicProPageWithHeader from '../../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { useThemeComponents } from '../../../../themes/hooks';
import ApplicationList, { ApplicationColumns } from '../../../shared/ApplicationList';
import { useApplicationColumnsForCI } from '../../../shared/ApplicationList/helpers';
import ApplicationDownloadModal from '../../../shared/ApplicationListPage/ApplicationDownloadModal';
import { allOption } from '../../../shared/ApplicationListPage/helpers';
import ListingWrapper from '../../../shared/CIPage/ListingWrapper';

export type ApplicationListPageProps = {
    endpoint: ApplicationListEndpointContextDataFragment;
    namespace: string;
};

const ApplicationListPageInner = ({ endpoint, namespace }: ApplicationListPageProps) => {
    const { t } = useTranslation(namespace);
    const stage = endpoint.applicationStage;
    const [visible, setVisible] = useState(false);
    const [moduleListByApplication, setModuleListByApplication] = useState<string[]>([]);
    const { Button } = useThemeComponents();
    const { dealerId } = useSingleDealerId();

    const [downloading, setDownloading] = useState(false);

    const company = useCompany(true);
    const { token } = useAccountContext();
    const { formatOptions } = useDownloadOptions({ includeCapFormat: false, includeReportingFormat: true });
    const language = useLanguage();

    const setDefault = useCallback(() => {
        setVisible(false);
        setDownloading(false);
    }, [setVisible, setDownloading]);

    const { data, loading } = useListModulesForApplicationDownloadQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyIds: company ? [company.id] : [],
                moduleRole: stage === ApplicationStage.Mobility ? ModuleRole.Mobility : ModuleRole.Application,
            },
        },
    });

    const modules = data?.modules;

    const filteredModules = useMemo(
        () =>
            (modules || []).filter(
                module =>
                    moduleListByApplication.includes(module.id) &&
                    (module.__typename === 'StandardApplicationModule' ||
                        module.__typename === 'EventApplicationModule' ||
                        module.__typename === 'ConfiguratorModule' ||
                        module.__typename === 'MobilityModule' ||
                        module.__typename === 'FinderApplicationPublicModule' ||
                        module.__typename === 'FinderApplicationPrivateModule' ||
                        module.__typename === 'LaunchPadModule')
            ),
        [moduleListByApplication, modules]
    );

    const channelModuleOption = useMemo(() => {
        const options = filteredModules.map(module => ({ label: module.displayName, value: module.id }));

        return [...(company && options.length > 1 ? [allOption] : []), ...options];
    }, [company, filteredModules]);

    const columnList = useApplicationColumnsForCI(stage);
    const showColumns = useMemo(() => {
        if (!company) {
            columnList.unshift(ApplicationColumns.Company);
        }

        return columnList;
    }, [columnList, company]);

    const openDownloadModal = useCallback(() => {
        setVisible(true);
    }, [setVisible]);

    const displayDownloadFormats = useMemo(
        () =>
            [ApplicationStage.Appointment, ApplicationStage.Reservation, ApplicationStage.VisitAppointment].includes(
                stage
            ),
        [stage]
    );

    const extra =
        stage === ApplicationStage.TradeIn || stage === ApplicationStage.FollowUp ? null : (
            <Button icon={<DownloadOutlined />} onClick={openDownloadModal} type="primary">
                {t('actions.download')}
            </Button>
        );
    if (loading) {
        return <LoadingElement />;
    }

    return (
        <BasicProPageWithHeader extra={extra} style={{ height: '100%' }} title={endpoint.title}>
            <ApplicationDownloadModal
                channelModuleOption={channelModuleOption}
                currentLanguageId={language?.currentLanguageId || ''}
                dealerIds={[dealerId]}
                downloading={downloading}
                formatOptions={formatOptions}
                formatVisible={displayDownloadFormats}
                modules={modules}
                namespace={namespace}
                setDefault={setDefault}
                setDownloading={setDownloading}
                stage={stage}
                token={token}
                visible={visible}
                isLargeExport
            />
            <ListingWrapper>
                <ApplicationList
                    dealerIds={[dealerId]}
                    moduleIds={endpoint.applicationModuleIds}
                    setModuleListByApplication={setModuleListByApplication}
                    showColumns={showColumns}
                    stage={endpoint.applicationStage}
                    forCi
                />
            </ListingWrapper>
        </BasicProPageWithHeader>
    );
};

export default ApplicationListPageInner;
