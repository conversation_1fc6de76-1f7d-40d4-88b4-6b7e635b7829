import { PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import type { ApplicationAgreementDataFragment } from '../../../../../../api/fragments/ApplicationAgreementData';
import FormAutoTouch from '../../../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../../../components/ScrollToTop';
import Form from '../../../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useTranslatedString from '../../../../../../utilities/useTranslatedString';
import usePaymentSectionColSpans from '../../../../../shared/PaymentPage/shared/usePaymentSectionColSpans';
import JourneyToolbar from '../../../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import Banner from '../../../ApplicantForm/Banner';
import JourneySectionWrapper from '../../../shared/JourneySectionWrapper';
import JourneySteps from '../../../shared/JourneySteps';
import useEventHeaderLogo from '../../../useEventHeaderLogo';
import { type PaymentPageInnerWrapperProps as OldPaymentPageInnerWrapperProps } from '../../shared/PaymentPageInnerWrapper';
import BookingDeposit from './BookingDeposit';
import PaymentAgreements from './PaymentAgreements';

type PaymentPageInnerWrapperProps = Omit<OldPaymentPageInnerWrapperProps, 'children'> & {
    children?: React.ReactNode;
    depositAmount: number;
    paymentAgreements: ApplicationAgreementDataFragment[];
};

const PaymentPageInnerWrapper = ({
    application,
    children,
    depositAmount,
    formName,
    handleSubmit,
    paymentAgreements,
    stage,
    stages,
    toolbarButtons,
}: PaymentPageInnerWrapperProps) => {
    const { event } = application;
    const { t } = useTranslation(['paymentDetails']);
    const translatedString = useTranslatedString();
    const [halfColSpan] = usePaymentSectionColSpans();
    const { StandardLayout } = useThemeComponents();

    useEventHeaderLogo(event?.hasCustomiseBanner && !!event?.banner ? 'dark' : 'light');

    return (
        <StandardLayout
            {...(!event?.hasCustomiseBanner && {
                title: translatedString(event.name),
            })}
            customFooterHeight={0}
        >
            {event?.hasCustomiseBanner && event?.banner && <Banner banner={event?.banner} />}
            <BasicProLayoutContainer id="formSection">
                <JourneySteps currentStage={stage} stages={stages} />
                <FormAutoTouch />
                <Form id={formName} name={formName} onSubmitCapture={handleSubmit}>
                    <ScrollToTop />
                    <Row gutter={[24, 24]}>
                        <Col span={24}>
                            <JourneySectionWrapper application={application} stage={stage} stages={stages}>
                                <Row gutter={[24, 24]}>
                                    <PaymentAgreements
                                        depositAmount={depositAmount}
                                        paymentAgreements={paymentAgreements}
                                    />
                                    {depositAmount > 0 && (
                                        <Col {...halfColSpan}>
                                            <Row gutter={[16, 16]}>
                                                <Col span={24}>
                                                    <PText size="medium" weight="bold">
                                                        {t('paymentDetails:titles.paymentTitle')}
                                                    </PText>
                                                </Col>
                                                <Col span={24}>
                                                    <div style={{ marginBottom: '15px' }}>
                                                        <BookingDeposit depositAmount={depositAmount} />
                                                    </div>
                                                </Col>
                                                <Col span={24}>{children}</Col>
                                            </Row>
                                        </Col>
                                    )}
                                </Row>
                            </JourneySectionWrapper>
                        </Col>
                    </Row>
                </Form>
            </BasicProLayoutContainer>
            <JourneyToolbar className="journey-toolbar">{toolbarButtons.map(button => button)}</JourneyToolbar>
        </StandardLayout>
    );
};

export default PaymentPageInnerWrapper;
