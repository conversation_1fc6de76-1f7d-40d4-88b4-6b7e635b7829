import { PHeading } from '@porsche-design-system/components-react';
import { Col, Divider, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import Form from '../../../../components/fields/Form';
import { defaultFilterOption } from '../../../../components/fields/SelectField';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import Checkbox from '../../../../themes/porscheV3/Checkbox/Checkbox';
import NearbyDealer from '../../../../themes/porscheV3/Fields/NearbyDealer';
import SelectField from '../../../../themes/porscheV3/Fields/SelectField';
import Tooltip from '../../../../themes/porscheV3/Tooltip';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import {
    hasAppointmentScenario,
    hasVisitAppointmentScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared/scenarios';
import { StyledInfoCircleFilled } from '../../../shared/CIPage/ui';
import { StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import AppointmentDetailsSection, { AppointmentType } from '../ApplicantForm/AppointmentDetailsSection';
import Banner from '../ApplicantForm/Banner';
import DealershipVehicleInterest from '../ApplicantForm/DealershipVehicleInterest';
import LiveChat from '../ApplicantForm/LiveChat';
import { allowedDealerInfoField } from '../ApplicantForm/shared';
import { useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import JourneySectionWrapper from '../shared/JourneySectionWrapper';
import JourneySteps from '../shared/JourneySteps';
import NextButton from '../shared/NextButton';
import useEventHeaderLogo from '../useEventHeaderLogo';
import useModelVariantOptions from './shared/useModelVariantOptions';
import type { RequiredDetailsFormValues, StageProps } from './types';
import { CheckboxContainer, FieldRowContainer, RequiredDetailsSectionContainer } from './ui';

const colSpan = { xs: 24, lg: 12, xl: 6 };

const InnerForm = ({ stage, stages }: StageProps) => {
    const company = useCompany();

    const { t } = useTranslation([
        'eventApplicantForm',
        'eventRequiredDetails',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'configuratorJourney',
        'appointmentPage',
    ]);
    const translatedString = useTranslatedString();

    const { StandardLayout } = useThemeComponents();

    const { event, eventModule, liveChatSetting, variants, showLiveChat } = useEventJourneySetupContext();

    const { values, isSubmitting, isValid, handleSubmit, setFieldValue } =
        useFormikContext<RequiredDetailsFormValues>();

    const [modelOptions, variantOptions] = useModelVariantOptions({
        dealerId: values.dealerId,
        modelId: values.modelId,
        availableLocalVariantsForModule: variants,
        dealerVehicles: event.dealerVehicles,
    });

    const { isAllowTestDrive, isAllowShowroomVisit, isAllowTradeIn } = useMemo(
        () => ({
            isAllowTestDrive:
                (hasAppointmentScenario(event.scenarios) && event.isAllowTestDrive) || values.configuration.testDrive,
            isAllowShowroomVisit: hasVisitAppointmentScenario(event.scenarios) || values.configuration.visitAppointment,
            isAllowTradeIn: event.isAllowTradeIn || values.configuration.tradeIn,
        }),
        [
            event.isAllowTestDrive,
            event.isAllowTradeIn,
            event.scenarios,
            values.configuration.testDrive,
            values.configuration.tradeIn,
            values.configuration.visitAppointment,
        ]
    );

    const { showTestDriveDatePicker, showShowroomVisitDatePicker } = useMemo(
        () => ({
            showTestDriveDatePicker:
                eventModule?.appointmentModule &&
                hasAppointmentScenario(event.scenarios) &&
                eventModule?.displayAppointmentDatepicker &&
                values.configuration.testDrive,
            showShowroomVisitDatePicker:
                eventModule?.visitAppointmentModule &&
                hasVisitAppointmentScenario(event.scenarios) &&
                eventModule?.displayAppointmentDatepicker &&
                values.configuration.visitAppointment,
        }),
        [
            event.scenarios,
            eventModule?.appointmentModule,
            eventModule?.displayAppointmentDatepicker,
            eventModule?.visitAppointmentModule,
            values.configuration.testDrive,
            values.configuration.visitAppointment,
        ]
    );

    const onDealerChange = useCallback(() => {
        setFieldValue('modelId', null);
        setFieldValue('vehicleId', null);
    }, [setFieldValue]);

    const onModelChange = useCallback(() => {
        setFieldValue('vehicleId', null);
    }, [setFieldValue]);

    const title = useMemo(() => {
        if (event?.showDealership && !event?.hasVehicleIntegration) {
            return t('eventRequiredDetails:title.dealership');
        }

        if (!event?.showDealership && event?.hasVehicleIntegration) {
            return t('eventRequiredDetails:title.vehicle');
        }

        return t('eventRequiredDetails:title.main');
    }, [event, t]);

    useEventHeaderLogo(event?.hasCustomiseBanner && !!event?.banner ? 'dark' : 'light');

    return (
        <>
            (
            <StandardLayout
                {...(!event?.hasCustomiseBanner && {
                    title: translatedString(event.name),
                })}
                customFooterHeight={0}
            >
                {event?.hasCustomiseBanner && event?.banner && <Banner banner={event?.banner} />}
                <BasicProLayoutContainer id="formSection">
                    <JourneySteps currentStage={stage} stages={stages} />
                    <FormAutoTouch />
                    <Form
                        data-cy="lcfRequiredFormId"
                        id="lcfRequiredFormId"
                        name="lcfRequiredFormId"
                        onSubmitCapture={handleSubmit}
                    >
                        <Row gutter={[0, 68]}>
                            <Col span={24}>
                                <JourneySectionWrapper stage={stage} stages={stages}>
                                    <RequiredDetailsSectionContainer>
                                        <PHeading size="medium">{title}</PHeading>
                                        <FieldRowContainer gutter={[16, 0]}>
                                            {event?.showDealership && (
                                                <Col {...colSpan}>
                                                    {company?.findNearbyDealer ? (
                                                        <NearbyDealer
                                                            allowedInfo={allowedDealerInfoField}
                                                            data-cy="vehicleInterestDealer"
                                                            event={event}
                                                            label={t(
                                                                'eventApplicantForm:fields.findNearbyDealer.label'
                                                            )}
                                                            name="dealerId"
                                                            onChange={onDealerChange}
                                                            required
                                                        />
                                                    ) : (
                                                        <DealershipVehicleInterest
                                                            {...t('eventApplicantForm:fields.preferredDealership', {
                                                                returnObjects: true,
                                                            })}
                                                            allowedInfo={allowedDealerInfoField}
                                                            data-cy="vehicleInterestDealer"
                                                            event={event}
                                                            name="dealerId"
                                                            onChange={onDealerChange}
                                                            isTranslatedOption
                                                            required
                                                        />
                                                    )}
                                                </Col>
                                            )}
                                            {event?.hasVehicleIntegration && (
                                                <>
                                                    <Col {...colSpan}>
                                                        <SelectField
                                                            {...t('eventRequiredDetails:fields.model', {
                                                                returnObjects: true,
                                                            })}
                                                            data-cy="vehicleInterestSubmodel"
                                                            disabled={!values.dealerId}
                                                            filterOption={defaultFilterOption}
                                                            name="modelId"
                                                            onChange={onModelChange}
                                                            options={modelOptions}
                                                            required
                                                            showSearch
                                                        />
                                                    </Col>
                                                    <Col {...colSpan}>
                                                        <SelectField
                                                            {...t('eventRequiredDetails:fields.variant', {
                                                                returnObjects: true,
                                                            })}
                                                            data-cy="vehicleInterestVariant"
                                                            disabled={!values.modelId}
                                                            filterOption={defaultFilterOption}
                                                            name="vehicleId"
                                                            options={variantOptions}
                                                            required
                                                            showSearch
                                                        />
                                                    </Col>
                                                </>
                                            )}
                                        </FieldRowContainer>
                                        <Row gutter={[16, 16]}>
                                            <Divider />
                                            <Col span={24}>
                                                <Row gutter={[16, 16]}>
                                                    {isAllowTestDrive && (
                                                        <Col span={24}>
                                                            <CheckboxContainer>
                                                                <CheckboxField
                                                                    customComponent={Checkbox}
                                                                    name="configuration.testDrive"
                                                                >
                                                                    {t('eventRequiredDetails:fields.testDrive.label')}
                                                                </CheckboxField>
                                                                <Tooltip
                                                                    placement="top"
                                                                    title={t(
                                                                        'eventRequiredDetails:fields.testDrive.tooltip'
                                                                    )}
                                                                >
                                                                    <StyledInfoCircleFilled />
                                                                </Tooltip>
                                                            </CheckboxContainer>
                                                        </Col>
                                                    )}

                                                    {showTestDriveDatePicker && (
                                                        <Col lg={12} xs={24}>
                                                            <AppointmentDetailsSection
                                                                applicationModule={event.module}
                                                                appointmentType={AppointmentType.Appointment}
                                                                colSpan={{ lg: 12, xs: 24 }}
                                                                event={event}
                                                                showSkipValidation={event.privateAccess}
                                                            />
                                                        </Col>
                                                    )}

                                                    {isAllowShowroomVisit && (
                                                        <Col span={24}>
                                                            <CheckboxContainer>
                                                                <CheckboxField
                                                                    customComponent={Checkbox}
                                                                    name="configuration.visitAppointment"
                                                                >
                                                                    {t(
                                                                        'eventRequiredDetails:fields.requestShowroomVisit.label'
                                                                    )}
                                                                </CheckboxField>
                                                            </CheckboxContainer>
                                                        </Col>
                                                    )}

                                                    {showShowroomVisitDatePicker && (
                                                        <Col lg={12} xs={24}>
                                                            <AppointmentDetailsSection
                                                                applicationModule={event.module}
                                                                appointmentType={AppointmentType.VisitAppointment}
                                                                colSpan={{ lg: 12, xs: 24 }}
                                                                event={event}
                                                                showSkipValidation={event.privateAccess}
                                                            />
                                                        </Col>
                                                    )}

                                                    {isAllowTradeIn && (
                                                        <Col span={24}>
                                                            <CheckboxContainer>
                                                                <CheckboxField
                                                                    customComponent={Checkbox}
                                                                    name="configuration.tradeIn"
                                                                >
                                                                    {t('eventRequiredDetails:fields.tradeIn.label')}
                                                                </CheckboxField>
                                                            </CheckboxContainer>
                                                        </Col>
                                                    )}
                                                </Row>
                                            </Col>
                                        </Row>
                                    </RequiredDetailsSectionContainer>
                                </JourneySectionWrapper>
                            </Col>
                        </Row>
                    </Form>
                    {showLiveChat && <LiveChat chatSetting={liveChatSetting} variants={variants} />}
                </BasicProLayoutContainer>
            </StandardLayout>
            <StyledJourneyToolbar className="journey-toolbar">
                <NextButton
                    data-cy="leadGenFormButton"
                    disabled={isSubmitting || !isValid}
                    form="lcfRequiredFormId"
                    onSubmit={handleSubmit}
                />
            </StyledJourneyToolbar>
            );
        </>
    );
};

export default InnerForm;
