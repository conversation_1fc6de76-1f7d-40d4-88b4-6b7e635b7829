/* eslint-disable import/prefer-default-export */
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';
import DefaultNextButton from './NextButton';

export type NextButtonProps = {
    form?: string;
    onSubmit?: () => void;
    disabled?: boolean;
    overrideTheme?: 'dark' | 'light';
};

export const NextButton = ({ form, onSubmit, disabled, overrideTheme }: NextButtonProps) => {
    const { t } = useTranslation('customerDetails');
    const { layout } = useRouter();
    const porscheTheme = useMemo(() => {
        if (overrideTheme) {
            return overrideTheme;
        }

        return layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined;
    }, [layout, overrideTheme]);

    return (
        <DefaultNextButton
            key="submit"
            disabled={disabled}
            form={form}
            htmlType="button"
            onClick={onSubmit}
            porscheTheme={porscheTheme}
            type="primary"
        >
            {t('customerDetails:nextButton')}
        </DefaultNextButton>
    );
};
