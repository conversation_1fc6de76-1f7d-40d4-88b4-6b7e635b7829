/* eslint-disable max-len */
import { DownloadOutlined } from '@ant-design/icons';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { LeadListEndpointContextDataFragment } from '../../../../api/fragments/LeadListEndpointContextData';
import { useListModulesForApplicationDownloadQuery } from '../../../../api/queries/listModulesForApplicationDownload';
import { ModuleRole } from '../../../../api/types';
import LoadingElement from '../../../../components/LoadingElement';
import { useAccountContext } from '../../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import useDownloadOptions from '../../../../components/fields/DownloadModal/useDownloadOptions';
import BasicProPageWithHeader from '../../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { useThemeComponents } from '../../../../themes/hooks';
import { ApplicationColumns } from '../../../shared/ApplicationList';
import ListingWrapper from '../../../shared/CIPage/ListingWrapper';
import LeadList from '../../../shared/LeadList';
import LeadDownloadModal from '../../../shared/LeadList/LeadDownloadModal';
import { allOption, allowedCapFormatTypes, useLeadColumns } from '../../../shared/LeadList/helpers';

export type LeadListPageProps = {
    endpoint: LeadListEndpointContextDataFragment;
};

const LeadListPageInner = ({ endpoint }: LeadListPageProps) => {
    const { t } = useTranslation('leadListPage');
    const { leadStage: stage } = endpoint;
    const [visible, setVisible] = useState(false);
    const [moduleListByLead, setModuleListByLead] = useState<string[]>([]);
    const { Button } = useThemeComponents();
    const { dealerId } = useSingleDealerId();

    const [formatVisible, setFormatVisible] = useState(false);
    const [downloading, setDownloading] = useState(false);

    const company = useCompany(true);
    const { token } = useAccountContext();
    const { formatOptions } = useDownloadOptions({ includeReportingFormat: true });
    const language = useLanguage();

    const setDefault = useCallback(() => {
        setVisible(false);
        setFormatVisible(false);
        setDownloading(false);
    }, [setVisible, setFormatVisible, setDownloading]);

    const { data, loading } = useListModulesForApplicationDownloadQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { companyIds: company ? [company.id] : [], moduleRole: ModuleRole.Lead } },
    });

    const modules = data?.modules;

    const filteredModules = useMemo(
        () =>
            (modules || []).filter(
                module =>
                    moduleListByLead.includes(module.id) &&
                    (module.__typename === 'StandardApplicationModule' ||
                        module.__typename === 'EventApplicationModule' ||
                        module.__typename === 'ConfiguratorModule' ||
                        module.__typename === 'MobilityModule' ||
                        module.__typename === 'FinderApplicationPublicModule' ||
                        module.__typename === 'FinderApplicationPrivateModule' ||
                        module.__typename === 'LaunchPadModule' ||
                        module.__typename === 'PorscheRetainModule')
            ),
        [moduleListByLead, modules]
    );

    const channelModuleOption = useMemo(() => {
        const options = filteredModules.map(module => ({ label: module.displayName, value: module.id }));

        return [...(company && options.length > 1 ? [allOption] : []), ...options];
    }, [company, filteredModules]);

    useEffect(() => {
        if (!module && channelModuleOption && channelModuleOption.length === 1) {
            const selectedModuleId = channelModuleOption[0].value;

            const foundModule = modules.find(module => module.id === selectedModuleId);
            if (foundModule && allowedCapFormatTypes.includes(foundModule.__typename)) {
                setFormatVisible(true);
            }
        }
    }, [modules, channelModuleOption]);

    const isAllCapModules = useMemo(
        () => filteredModules.every(module => allowedCapFormatTypes.includes(module.__typename)),
        [filteredModules]
    );

    const onChannelChange = useCallback(
        (value: string) => {
            const foundModule = modules.find(module => module.id === value);
            if (
                allowedCapFormatTypes.includes(foundModule?.__typename) ||
                (value === allOption.value && isAllCapModules)
            ) {
                setFormatVisible(true);
            } else {
                setFormatVisible(false);
            }
        },
        [isAllCapModules, modules]
    );

    useEffect(() => {
        if (channelModuleOption?.length === 1) {
            const selectedModuleId = channelModuleOption[0].value;

            const foundModule = modules.find(module => module.id === selectedModuleId);
            if (foundModule && allowedCapFormatTypes.includes(foundModule.__typename)) {
                setFormatVisible(true);
            }
        }
    }, [modules, channelModuleOption, visible]);

    const columnList = useLeadColumns({ forCi: true });
    const columns = useMemo(() => {
        if (!company) {
            columnList.unshift(ApplicationColumns.Company);
        }

        return columnList;
    }, [company, columnList]);

    const openDownloadModal = useCallback(() => {
        setVisible(true);
    }, [setVisible]);

    const extra = (
        <Button icon={<DownloadOutlined />} onClick={openDownloadModal} type="primary">
            {t('actions.download')}
        </Button>
    );

    if (loading) {
        return <LoadingElement />;
    }

    return (
        <BasicProPageWithHeader extra={extra} style={{ height: '100%' }} title={endpoint.title}>
            <LeadDownloadModal
                channelModuleOption={channelModuleOption}
                currentLanguageId={language?.currentLanguageId}
                dealerIds={[dealerId]}
                downloading={downloading}
                formatOptions={formatOptions}
                formatVisible={formatVisible}
                modules={modules}
                namespace="leadListPage"
                onChannelChange={onChannelChange}
                setDefault={setDefault}
                setDownloading={setDownloading}
                stage={stage}
                token={token}
                visible={visible}
                isLargeExport
            />
            <ListingWrapper>
                <LeadList
                    dealerIds={[dealerId]}
                    desiredColumns={columns}
                    leadStage={endpoint.leadStage}
                    setModuleListByLead={setModuleListByLead}
                    forCi
                />
            </ListingWrapper>
        </BasicProPageWithHeader>
    );
};

export default LeadListPageInner;
