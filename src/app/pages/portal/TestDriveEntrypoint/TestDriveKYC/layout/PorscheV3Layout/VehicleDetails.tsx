import { PText } from '@porsche-design-system/components-react';
import { Col } from 'antd';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import styled from 'styled-components';
import breakpoints from '../../../../../../utilities/breakpoints';
import useCompanyFormats from '../../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../../utilities/useTranslatedString';
import Media from '../../../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';
import calculateTotalPrice from '../../../../ConfiguratorApplicationEntrypoint/helper';
import FinderVehicleInfo from '../../../../FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInfo';
import type { TestDriveJourneyState } from '../../../Journey/shared';

const VehicleInfoContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
`;

const VehicleInfoDetails = styled.div`
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
`;

const MediaContainer = styled.div`
    & .ant-image {
        width: 180px;
        margin: auto;
        & > img {
            border-radius: var(--card-border-radius, initial);
            aspect-ratio: 16/9;
            object-fit: cover;
        }
    }

    @media (max-width: ${breakpoints.sm}) {
        & .ant-image {
            width: auto;
            height: 147px;
        }
    }
`;

const VehicleInfo = ({
    variant,
    totalPrice,
    filename,
    source,
}: {
    variant: string;
    totalPrice: number;
    filename: string;
    source: string;
}) => {
    const formats = useCompanyFormats();

    return (
        <VehicleInfoContainer>
            <MediaContainer>
                <Media fileName={filename} source={source} />
            </MediaContainer>
            <VehicleInfoDetails>
                <PText size="medium" weight="bold">
                    {variant}
                </PText>
                {!isNil(totalPrice) && <PText>{formats.formatAmountWithCurrency(totalPrice)}</PText>}
            </VehicleInfoDetails>
        </VehicleInfoContainer>
    );
};

const getVehiclePrice = (application: TestDriveJourneyState) => {
    switch (application.__typename) {
        case 'ConfiguratorApplication':
            return calculateTotalPrice(application);

        case 'StandardApplication':
            return application.financing?.totalPrice;

        case 'EventApplication':
            return application.vehicle?.__typename === 'LocalVariant' ? application.vehicle.vehiclePrice : null;

        default:
            return null;
    }
};

const VehicleDetails = ({ application }: { application: TestDriveJourneyState }) => {
    const translatedString = useTranslatedString();

    const vehicleData = useMemo(() => {
        if (
            application.__typename === 'ConfiguratorApplication' ||
            application.__typename === 'EventApplication' ||
            application.__typename === 'StandardApplication'
        ) {
            if (application.vehicle?.__typename === 'LocalVariant') {
                return {
                    make: translatedString(
                        application.vehicle.model.parentModel
                            ? application.vehicle.model.parentModel.make.name
                            : application.vehicle.model.make.name
                    ),
                    variant: translatedString(application.vehicle.name),
                    totalPrice: getVehiclePrice(application),
                    filename:
                        application.__typename === 'ConfiguratorApplication'
                            ? application.vehicleImage?.filename
                            : application.vehicle?.images?.[0]?.filename,
                    source:
                        application.__typename === 'ConfiguratorApplication'
                            ? application.vehicleImage?.url
                            : application.vehicle?.images?.[0]?.url,
                };
            }
        }

        return null;
    }, [application, translatedString]);

    if (!vehicleData && application.__typename !== 'FinderApplication') {
        return null;
    }

    return (
        <Col xs={24}>
            {application.__typename === 'FinderApplication' ? (
                <FinderVehicleInfo application={application} />
            ) : (
                <VehicleInfo {...vehicleData} />
            )}
        </Col>
    );
};

export default VehicleDetails;
