import { PHeading } from '@porsche-design-system/components-react';
import { Col, Row, Space } from 'antd';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { CustomerKind } from '../../../../../../api/types';
import ScrollToTop from '../../../../../../components/ScrollToTop';
import Form from '../../../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../../themes/hooks';
import CustomerDetails from '../../../../../shared/JourneyPage/CustomerDetails';
import JourneySteps from '../../../../../shared/JourneyPage/JourneySteps';
import { JourneyStepsProvider } from '../../../../../shared/JourneyPage/JourneyStepsContext';
import ConsentsAndDeclarations from '../../../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
import { fullWidthColSpan } from '../../../../StandardApplicationEntrypoint/KYCPage/shared';
import { StyledJourneyToolbar } from '../../../../StandardApplicationEntrypoint/styledComponents';
import type { TestDriveKYCJourneyValues, InnerProps } from '../../types';
import ApplicantIntegrationSection from './ApplicationIntegrationSection';
import NextButton from './NextButton';
import VehicleDetails from './VehicleDetails';

const InnerWrapper = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 32px;
`;

const InnerKYCSection = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 16px;
`;

// ensure inputs in a form with two inputs and a button to have their content graciously visible
const StyledCol = styled(Col)`
    @media (min-width: 993px) and (max-width: 1200px) {
        display: block;
        flex: 0 0 100%;
        max-width: 100%;
    }
`;

const PorscheV3Layout = ({
    state,
    endpoint,
    formName,
    title,
    kycPresets,
    kycAgreements,
    guarantorKYC,
    kycExtraSettings,
    setWithMyInfo,
    withMyInfo,
    proceedWithCustomerButton,
    proceedWithCustomer,
    uploadDocument,
    removeDocument,
    setPrefill,
    onSubmit,
    resetFormHandler,
    hasCorporatePreset,
}: InnerProps) => {
    const { application, stage, stages } = state;
    const { handleSubmit, isSubmitting } = useFormikContext<TestDriveKYCJourneyValues>();

    const { StandardLayout } = useThemeComponents();
    const { t } = useTranslation('customerDetails');

    if (application.__typename === 'LaunchpadApplication') {
        return null;
    }

    return (
        <>
            <StandardLayout customFooterHeight={0} title={title}>
                <BasicProLayoutContainer>
                    <JourneyStepsProvider
                        value={{
                            onBack: null,
                            shouldAllowBack: false,
                        }}
                    >
                        <JourneySteps currentStage={stage} stages={stages} />
                        <Form data-cy={formName} id={formName} name={formName} onSubmitCapture={handleSubmit}>
                            <ScrollToTop />
                            <Row>
                                <StyledCol span={24}>
                                    <InnerWrapper>
                                        <div className="v3-layout-card">
                                            <VehicleDetails application={state.application} />
                                        </div>
                                        <InnerKYCSection className="v3-layout-card">
                                            <PHeading size="large">{t('customerDetails:title')}</PHeading>
                                            <Space direction="vertical" size={20} style={{ width: '100%' }}>
                                                <ApplicantIntegrationSection
                                                    endpoint={endpoint}
                                                    hasProceedWithCustomerDeviceButton={proceedWithCustomerButton}
                                                    kycPresets={kycPresets}
                                                    proceedWithCustomerDevice={proceedWithCustomer}
                                                    setWithMyInfo={setWithMyInfo}
                                                    state={state}
                                                    withMyInfo={withMyInfo}
                                                />
                                                <CustomerDetails
                                                    colSpan={fullWidthColSpan}
                                                    customerKind={CustomerKind.Local}
                                                    hasGuarantorPreset={guarantorKYC.length > 0}
                                                    hasUploadDocuments={application.bank?.hasUploadDocuments}
                                                    hasVSOUpload={false}
                                                    isApplyingFromDetails={false}
                                                    kycExtraSettings={kycExtraSettings}
                                                    kycPresets={kycPresets}
                                                    removeDocument={removeDocument}
                                                    resetFormHandler={resetFormHandler}
                                                    setIsCorporate={null}
                                                    setPrefill={setPrefill}
                                                    showCommentsToInsurer={false}
                                                    showRemarks={false}
                                                    showResetButton={false}
                                                    showTabs={hasCorporatePreset}
                                                    uploadDocument={uploadDocument}
                                                    withFinancing={false}
                                                />
                                            </Space>
                                        </InnerKYCSection>
                                        {kycAgreements.length > 0 && (
                                            <div className="v3-layout-card">
                                                <ConsentsAndDeclarations
                                                    applicationAgreements={kycAgreements}
                                                    hideDivider={false}
                                                />
                                            </div>
                                        )}
                                    </InnerWrapper>
                                </StyledCol>
                            </Row>
                        </Form>
                    </JourneyStepsProvider>
                </BasicProLayoutContainer>
            </StandardLayout>
            <StyledJourneyToolbar className="journey-toolbar">
                <NextButton
                    data-cy={`${formName}Button`}
                    disabled={isSubmitting}
                    form={formName}
                    onSubmit={onSubmit}
                    overrideTheme="light"
                />
            </StyledJourneyToolbar>
        </>
    );
};

export default PorscheV3Layout;
