import { useApolloClient } from '@apollo/client';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    GetCustomerFieldsFromFilesDocument,
    type GetCustomerFieldsFromFilesQuery,
    type GetCustomerFieldsFromFilesQueryVariables,
} from '../../../../../../api/queries/getCustomerFieldsFromFiles';
import { useCompany } from '../../../../../../components/contexts/CompanyContextManager';
import { useOcrDetectedHandler } from '../../../../../../components/ocr';
import { useOcrFilesManagerContext } from '../../../../../../components/ocr/OcrFilesManager';
import OcrModal from '../../../../../../components/ocr/OcrModal';
import Button from '../../../../../../themes/porscheV3/Button';
import notification from '../../../../../../themes/porscheV3/notification';
import getApolloErrors from '../../../../../../utilities/getApolloErrors';
import type { ApplicantKYCIntegrationSectionRef } from '../../../../../shared/JourneyPage/ApplicantKYCIntegrationSection';
import ApplicationIntegrationSection from '../../../../../shared/JourneyPage/ApplicantKYCIntegrationSection';
import OcrAndMyinfo, { getApplicationModuleFromEndpoint, retrievemyInfoEnabled } from '../../../Shared/OcrAndMyinfo';
import type { InnerProps } from '../../types';

type ApplicationIntegrationSectionProps = Pick<
    InnerProps,
    'endpoint' | 'kycPresets' | 'setWithMyInfo' | 'withMyInfo' | 'state'
> & {
    hasProceedWithCustomerDeviceButton?: boolean;
    proceedWithCustomerDevice?: (values: any) => void;
};
const ApplicantIntegrationSection = ({
    endpoint,
    kycPresets,
    setWithMyInfo,
    withMyInfo,
    state,
    hasProceedWithCustomerDeviceButton,
    proceedWithCustomerDevice,
}: ApplicationIntegrationSectionProps) => {
    const integrationRef = useRef<ApplicantKYCIntegrationSectionRef>(null);

    const { t } = useTranslation([
        'eventApplicantForm',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'capApplication',
        'launchpadLeadDetails',
        'launchpadLeadList',
        'applicationDetails',
    ]);
    const company = useCompany();

    const { application } = state;

    // MyInfo Integration functions
    const myInfoEnabled = !!retrievemyInfoEnabled(application, endpoint);

    // OCR Integration functions
    const [ocrModalVisible, setOcrModalVisible] = useState(false);
    const ocrModalHandlers = useMemo(
        () => ({ hideModal: () => setOcrModalVisible(false), showModal: () => setOcrModalVisible(true) }),
        [setOcrModalVisible]
    );
    const onOcrDetected = useOcrDetectedHandler(kycPresets);
    const { files } = useOcrFilesManagerContext();
    const client = useApolloClient();
    const onOcrConfirm = useCallback(async () => {
        try {
            const response = await client.query<
                GetCustomerFieldsFromFilesQuery,
                GetCustomerFieldsFromFilesQueryVariables
            >({
                query: GetCustomerFieldsFromFilesDocument,
                variables: {
                    countryCode: company?.countryCode,
                    files: Object.entries(files).map(([kind, file]) => ({ kind, file })),
                },
                fetchPolicy: 'no-cache',
            });

            if (response.data?.fields) {
                onOcrDetected(response.data.fields);
            }
        } catch (error) {
            const apolloErrors = getApolloErrors(error);

            if (apolloErrors !== null) {
                const { $root: rootError } = apolloErrors;

                if (rootError) {
                    notification.error(rootError);
                }
            } else {
                console.error(error);
            }
        }
    }, [client, company?.countryCode, files, onOcrDetected]);

    const module = getApplicationModuleFromEndpoint(endpoint, application);
    const isOcrEnabled = module.__typename !== 'LaunchPadModule' && module.isOcrEnabled;

    const actionButtons = useMemo(
        () =>
            [
                company?.countryCode === 'SG' && !withMyInfo && myInfoEnabled && (
                    <div className="myinfo-button">
                        <OcrAndMyinfo
                            application={application}
                            endpoint={endpoint}
                            kycPresets={kycPresets}
                            setWithMyInfo={setWithMyInfo}
                            withMyInfo={withMyInfo}
                            singpassButtonOnly
                        />
                    </div>
                ),
                !withMyInfo && isOcrEnabled && (
                    <>
                        <Button porscheFallbackIcon="camera" type="secondary">
                            {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingId')}
                        </Button>
                        <OcrModal
                            hide={ocrModalHandlers.hideModal}
                            onConfirm={onOcrConfirm}
                            visible={ocrModalVisible}
                        />
                    </>
                ),
                hasProceedWithCustomerDeviceButton && (
                    <Button onClick={proceedWithCustomerDevice} porscheFallbackIcon="mobile" type="secondary">
                        {t('eventApplicantForm:applicantIntegrationSection.buttons.continueOnCustomerDevice')}
                    </Button>
                ),
            ].filter(Boolean),
        [
            application,
            company?.countryCode,
            endpoint,
            hasProceedWithCustomerDeviceButton,
            isOcrEnabled,
            kycPresets,
            myInfoEnabled,
            ocrModalHandlers.hideModal,
            ocrModalVisible,
            onOcrConfirm,
            proceedWithCustomerDevice,
            setWithMyInfo,
            t,
            withMyInfo,
        ]
    );

    if (!actionButtons.length) {
        return null;
    }

    return <ApplicationIntegrationSection ref={integrationRef} actionButtons={actionButtons} />;
};
export default ApplicantIntegrationSection;
