import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import Button from '../../../../../../themes/porscheV3/Button';
import type { NextButtonProps } from '../../../../StandardApplicationEntrypoint/shared/JourneyButton';

const NextButton = ({ form, onSubmit, disabled }: NextButtonProps) => {
    const { t } = useTranslation('customerDetails');
    const { isSubmitting } = useFormikContext();

    return (
        <Button
            key="submit"
            disabled={disabled || isSubmitting}
            form={form}
            htmlType="button"
            onClick={onSubmit}
            porscheTheme="light"
            type="primary"
        >
            {t('customerDetails:nextButton')}
        </Button>
    );
};

export default NextButton;
