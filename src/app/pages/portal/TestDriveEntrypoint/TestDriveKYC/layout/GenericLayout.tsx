import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { CustomerKind } from '../../../../../api/types';
import Form from '../../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import CustomerDetails from '../../../../shared/JourneyPage/CustomerDetails';
import ProceedWithCustomerButton from '../../../../shared/ProceedWithCustomerButton';
import ConsentsAndDeclarations from '../../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
import { NextButton } from '../../../StandardApplicationEntrypoint/shared/JourneyButton';
import { StyledJourneyToolbar } from '../../../StandardApplicationEntrypoint/styledComponents';
import OcrAndMyinfo from '../../Shared/OcrAndMyinfo';
import VehicleInterest from '../../Shared/VehicleOfInterest';
import type { TestDriveKYCJourneyValues, InnerProps } from '../types';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

const GenericLayout = ({
    state,
    formName,
    endpoint,
    proceedWithCustomerButton,
    proceedWithCustomer,
    title,
    kycPresets,
    kycAgreements,
    setWithMyInfo,
    withMyInfo,
    guarantorKYC,
    kycExtraSettings,
    uploadDocument,
    removeDocument,
    resetFormHandler,
    setPrefill,
    onSubmit,
    hasCorporatePreset,
}: InnerProps) => {
    const { application } = state;
    const { values, handleSubmit, isSubmitting } = useFormikContext<TestDriveKYCJourneyValues>();
    const { StandardLayout } = useThemeComponents();

    if (application.__typename === 'LaunchpadApplication') {
        return null;
    }

    return (
        <StandardLayout
            extra={
                proceedWithCustomerButton && <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values)} />
            }
            onBack={null}
            title={title}
            hasFooterBar
        >
            <BasicProLayoutContainer>
                <Form id={formName} name={formName} onSubmitCapture={handleSubmit}>
                    <Row gutter={[24, 50]}>
                        <Col {...leftColSpan}>
                            <Row gutter={[50, 50]}>
                                <VehicleInterest application={application} />
                                <OcrAndMyinfo
                                    application={application}
                                    endpoint={endpoint}
                                    kycPresets={kycPresets}
                                    setWithMyInfo={setWithMyInfo}
                                    withMyInfo={withMyInfo}
                                />
                            </Row>
                        </Col>

                        <Col {...rightColSpan}>
                            <Row gutter={[16, 16]}>
                                <Col span={24}>
                                    <CustomerDetails
                                        customerKind={CustomerKind.Local}
                                        hasGuarantorPreset={guarantorKYC.length > 0}
                                        hasUploadDocuments={application.bank?.hasUploadDocuments}
                                        hasVSOUpload={false}
                                        isApplyingFromDetails={false}
                                        kycExtraSettings={kycExtraSettings}
                                        kycPresets={kycPresets}
                                        removeDocument={removeDocument}
                                        resetFormHandler={resetFormHandler}
                                        setIsCorporate={null}
                                        setPrefill={setPrefill}
                                        showCommentsToInsurer={false}
                                        showRemarks={false}
                                        showResetButton={false}
                                        showTabs={hasCorporatePreset}
                                        uploadDocument={uploadDocument}
                                        withFinancing={false}
                                    />
                                </Col>
                                <Col span={24}>
                                    <ConsentsAndDeclarations
                                        applicationAgreements={kycAgreements}
                                        hideDivider={false}
                                    />
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </Form>
            </BasicProLayoutContainer>
            <StyledJourneyToolbar>
                <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
            </StyledJourneyToolbar>
        </StandardLayout>
    );
};

export default GenericLayout;
