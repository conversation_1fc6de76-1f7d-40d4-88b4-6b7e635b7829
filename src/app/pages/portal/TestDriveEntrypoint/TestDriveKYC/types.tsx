import type { Dispatch, SetStateAction } from 'react';
import type { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import type { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments/EventApplicationEntrypointContextData';
import type { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import type { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import type { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import type { CustomerKind, TradeInVehiclePayload, LocalCustomerManagementModule } from '../../../../api/types';
import type { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import type { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { Agreements } from '../../EventApplicationEntrypoint/ApplicantForm';
import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { KYCJourneyValues } from '../../StandardApplicationEntrypoint/KYCPage/shared';
import type { TestDriveJourneyState } from '../Journey/shared';

export type ApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type TestDriveKYCJourneyValues = {
    agreements: AgreementValues;
    customer: ApplicantFormValues;
    tradeInVehicle?: TradeInVehiclePayload[];
    prefix?: string;
    vsoUpload?: File[];
    uploadDocuments?: {
        [CustomerKind.Local]?: File[];
        [CustomerKind.Corporate]?: File[];
    };
    prefill: boolean;
};

export type TestDriveKYCPageProps = {
    state: State<TestDriveJourneyState>;
    dispatch: Dispatch<Action<TestDriveJourneyState>>;
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | EventApplicationEntrypointContextDataFragment
        | ConfiguratorApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
};

export type InnerProps = {
    state: TestDriveKYCPageProps['state'];
    formName: string;
    proceedWithCustomerButton: boolean;
    proceedWithCustomer: (currentValues: TestDriveKYCJourneyValues | KYCJourneyValues) => Promise<void>;
    title: JSX.Element;
    endpoint: TestDriveKYCPageProps['endpoint'];
    kycPresets: KycFieldSpecsFragment[];
    kycAgreements: Agreements;
    setWithMyInfo: Dispatch<SetStateAction<boolean>>;
    withMyInfo: boolean;
    guarantorKYC: KycFieldSpecsFragment[];
    hasCorporatePreset: boolean;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    setPrefill: Dispatch<SetStateAction<boolean>>;
    onSubmit: () => void;
    resetFormHandler: () => void;
} & UploadDocumentProp;
