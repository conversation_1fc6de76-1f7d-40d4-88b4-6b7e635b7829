import { Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import type { LocalCustomerManagementModule } from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import type { Agreements } from '../../EventApplicationEntrypoint/ApplicantForm';
// eslint-disable-next-line max-len
import useProceedWithCustomerDeviceButton from '../../StandardApplicationEntrypoint/KYCPage/useProceedWithCustomerDeviceButton';
import GenericLayout from './layout/GenericLayout';
import PorscheV3Layout from './layout/PorscheV3Layout';
import type { TestDriveKYCJourneyValues, TestDriveKYCPageProps, InnerProps } from './types';

type TestDriveKYCPageInnerProps = TestDriveKYCPageProps & {
    setPrefill: Dispatch<SetStateAction<boolean>>;
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    kycAgreements: Agreements;
    hasCorporatePreset: boolean;
} & UploadDocumentProp;

const TestDriveKYCPageInner = ({
    state,
    endpoint,
    dispatch,
    setPrefill,
    kycPresets,
    kycExtraSettings,
    kycAgreements,
    hasCorporatePreset,
    uploadDocument,
    removeDocument,
}: TestDriveKYCPageInnerProps) => {
    const { application } = state;

    const { t } = useTranslation('customerDetails');
    const { values, resetForm, initialValues, validateForm, submitForm } =
        useFormikContext<TestDriveKYCJourneyValues>();

    const { testDriveKYC, guarantorKYC } = useMemo(
        () => ({
            testDriveKYC: application.testDriveKYC,
            guarantorKYC: application.__typename !== 'LaunchpadApplication' ? application.guarantorKYC : [],
        }),
        [application]
    );

    const { proceedWithCustomerButton, proceedWithCustomer } = useProceedWithCustomerDeviceButton(
        testDriveKYC,
        state,
        dispatch
    );

    const [withMyInfo, setWithMyInfo] = useState(false);

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const title = useMemo(
        () => (
            <Row style={{ width: '100%' }}>
                <Typography>{t('customerDetails:title')}</Typography>
            </Row>
        ),
        [t]
    );

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    const InnerProps: InnerProps = useMemo(
        () => ({
            state,
            formName: 'testDriveKYC',
            endpoint,
            proceedWithCustomerButton,
            proceedWithCustomer,
            title,
            kycPresets,
            kycAgreements,
            setWithMyInfo,
            withMyInfo,
            guarantorKYC,
            kycExtraSettings,
            uploadDocument,
            removeDocument,
            resetFormHandler,
            setPrefill,
            onSubmit,
            hasCorporatePreset,
        }),
        [
            endpoint,
            guarantorKYC,
            hasCorporatePreset,
            kycAgreements,
            kycExtraSettings,
            kycPresets,
            onSubmit,
            proceedWithCustomer,
            proceedWithCustomerButton,
            removeDocument,
            resetFormHandler,
            setPrefill,
            state,
            title,
            uploadDocument,
            withMyInfo,
        ]
    );

    return (
        <>
            <FormAutoTouch />
            {application.__typename === 'EventApplication' ? (
                <PorscheV3Layout {...InnerProps} />
            ) : (
                <GenericLayout {...InnerProps} />
            )}
        </>
    );
};

export default TestDriveKYCPageInner;
