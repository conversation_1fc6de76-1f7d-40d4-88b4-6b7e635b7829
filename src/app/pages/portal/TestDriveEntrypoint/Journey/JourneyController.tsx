/* eslint-disable max-len */
import { useEffect, useMemo, useReducer, useRef } from 'react';
import { useNavigate } from 'react-router';
import type { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments/EventApplicationEntrypointContextData';
import type { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import type { LaunchPadApplicationEntrypointContextDataFragment } from '../../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import type { LeadListEndpointContextDataFragment } from '../../../../api/fragments/LeadListEndpointContextData';
import { ApplicationSigningPurpose } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { useRedirectWithBlock } from '../../../shared/blockBackRedirection';
import ConfirmEmailSendPage from '../../StandardApplicationEntrypoint/ConfirmationPage/ConfirmEmailSendPage';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import {
    NamirialPage,
    NamirialRejectPage,
    NamirialTimeoutPage,
} from '../../StandardApplicationEntrypoint/NamirialPage';
import NamirialRedirectPage from '../../StandardApplicationEntrypoint/NamirialPage/NamiralRedirectPage';
import OTPPage from '../../StandardApplicationEntrypoint/OTPPage';
import TestDriveKYC from '../TestDriveKYC';
import InitializeStage from './initializeStage';
import reducer from './reducer';
import type { TestDriveJourneyState } from './shared';

export type JourneyControllerProps = {
    initialToken: string;
    initialApplication: TestDriveJourneyState;
    initialStage: JourneyStage;
    endpoint:
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
        | LaunchPadApplicationEntrypointContextDataFragment
        | LeadListEndpointContextDataFragment;
};

const Redirect = ({ to }: { to: string }) => {
    useRedirectWithBlock(to);

    return <PortalLoadingElement />;
};

const JourneyController = ({ initialToken, initialApplication, initialStage, endpoint }: JourneyControllerProps) => {
    const initialState = useRef({
        token: initialToken,
        stage: initialStage,
        application: initialApplication,
        stages: getApplicationJourneyStages(initialApplication),
    });

    const navigate = useNavigate();
    const [state, dispatch] = useReducer(reducer, initialState.current);

    const journeyContent = useMemo(() => {
        switch (state.stage) {
            case JourneyStage.Initialize:
                return <InitializeStage dispatch={dispatch} state={state} />;

            case JourneyStage.TestDriveKYC:
                if (
                    endpoint.__typename !== 'LaunchPadApplicationEntrypoint' &&
                    endpoint.__typename !== 'LeadListEndpoint'
                ) {
                    return <TestDriveKYC dispatch={dispatch} endpoint={endpoint} state={state} />;
                }

                return <NotFoundResult />;

            case JourneyStage.TestDriveOtp:
                return <OTPPage dispatch={dispatch} purpose={ApplicationSigningPurpose.TestDrive} state={state} />;

            case JourneyStage.TestDriveNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.TestDrive} state={state} />;

            case JourneyStage.NamirialRedirect:
            case JourneyStage.TestDriveNamirialRedirect:
                return (
                    <NamirialRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.TestDrive}
                        state={state}
                    />
                );

            case JourneyStage.TestDriveNamirialReject:
                return <NamirialRejectPage />;

            case JourneyStage.TestDriveNamirialTimeout:
                return <NamirialTimeoutPage />;

            case JourneyStage.ConfirmEmailSend:
                return <ConfirmEmailSendPage />;

            case JourneyStage.Unknown:
            default:
                return <NotFoundResult />;
        }
    }, [endpoint, state]);

    useEffect(() => {
        // ensure we locally persist the latest token as well
        navigate('.', { state: { token: state.token }, replace: true });
    }, [state.token, navigate]);

    if (
        state.application.draftFlow.isTestDriveProcessSigningCompleted ||
        (state.stage === JourneyStage.Unknown &&
            !state.application.testDriveSigning &&
            state.application.draftFlow.areTestDriveAgreementsCompleted)
    ) {
        return <Redirect to={state.application.testDriveRedirectUrl} />;
    }

    return journeyContent;
};

export default JourneyController;
