// ant-design-v5-theme.ts
// Ant Design v5 theme configuration
// This replaces the Less variables from antd.override.less

import type { ThemeConfig } from 'antd';

// Ant Design v5 theme configuration
// This replaces the Less variables from antd.override.less
export const antdThemeConfig: ThemeConfig = {
    token: {
        // Convert Less variables to v5 design tokens
        // Based on your antd.override.less file:
        colorPrimary: 'var(--ant-primary-color)', // Keep CSS variable support for dynamic theming

        // Font family - CRITICAL: This ensures Porsche Next font is applied to all Ant Design components
        // Using CSS variable for dynamic theming support
        fontFamily: "var(--ant-font-family, 'Porsche Next','Arial Narrow',Arial,'Heiti SC',SimHei,sans-serif)",

        // Layout tokens (from variables.less)
        paddingContentHorizontalLG: 20, // @layout-body-padding horizontal
        paddingContentVertical: 10, // @layout-body-padding vertical

        // Add other commonly used tokens
        borderRadius: 2,
        wireframe: false,
    },
    components: {
        // Segmented component (from antd.override.less)
        Segmented: {
            itemSelectedBg: 'var(--ant-primary-color)', // @segmented-selected-bg
            itemColor: 'rgba(0, 0, 0, 0.65)', // @segmented-label-color
            itemHoverColor: 'var(--ant-primary-color)', // @segmented-label-hover-color
        },

        // Layout component
        Layout: {
            // From global.less body background
            bodyBg: 'var(--app-body-background-color, rgba(239, 239, 239, 0.93725))',
        },

        // Button component (spacing from global.less)
        Button: {
            // The margin styling is handled in CSS, but we can set consistent spacing
        },

        // Tooltip component (from global.less)
        Tooltip: {
            colorBgSpotlight: 'rgba(0, 0, 0, 0.8)', // Background from global.less
        },

        // Popover component
        Popover: {
            // Custom styles are handled in CSS
        },
    },
    // Enable CSS Variables for dynamic theming compatibility
    cssVar: {
        prefix: 'ant',
        key: 'default',
    },
};

// Alternative theme tokens for CSS variable fallback
export const v5ThemeTokens = {
    paddingContentHorizontalLG: 20,
    paddingContentVertical: 10,
    borderRadius: 2,
    // Add more tokens as needed
};

export default antdThemeConfig;
